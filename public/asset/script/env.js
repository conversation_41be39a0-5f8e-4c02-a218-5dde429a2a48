var domainUrl = $("#appUrl").val();
var sourceUrl = `${domainUrl}public/storage/`;

var app = {
    toastPosition: "topRight",
    fadeInAction: "fadeInDown",
    fadeOutAction: "fadeOutUp",
    transitionInAction: "fadeInDown",
    transitionOutAction: "fadeOutUp",
    timeout: 3000,
    greenToast: "green",
    redToast: "red",
    checkCircleIcon: `${domainUrl}asset/img/check-circle.svg`,
    cancleIcon: `${domainUrl}asset/img/x.svg`,
    thisUserHasBeenBlocked: "This user has been blocked",
    thisUserHasBeenUnblocked: "This user has been unblocked",
    thisUserisAllowedToGoLive: "This user is allowed to go live.",
    restrictLiveAccessToUser: "Restrict Live Access to User.",
    postDeleteSuccessfully: "Post Delete Successfully.",
    deleteLiveApplication: "Delete Live Application.",
    deleteRedeemRequest: "Delete Redeem Request.",
    diamondPackAdded: "Diamond Pack Added Successfully.",
    diamondPackUpdated: "Diamond Pack Updated Successfully.",
    diamondPackdeleted: "Diamond Pack Deleted Successfully.",
    giftAdded: "Gift Add Successfully.",
    giftUpdated: "Gift Update Successfully.",
    giftdeleted: "Gift Delete Successfully.",
    rejectVerificationRequest: "Reject Verification Request.",
    approveVerificationRequest: "Approve Verification Request.",
    rejectUserReport: "Reject User Report.",
    notificationSend: "Notification Send Successfully.",
    notificationUpdated: "Notification Updated.",
    notificationDeleted: "Notification Deleted.",
    interestUpdated: "Interest Updated.",
    interestDeleted: "Interest Deleted.",
    EntervailedPasswordandUsername: "Enter vailed Password and Username ",
    Error: "Oops",
    Success: "Success",
    settingUpdatedSuccessfully: "Setting Updated Successfully",
    LoginSuccessfull: "Login Successfull",
    sure: "Are you sure?",
    Yourpackagehasbeendeleted: "Your Package has been deleted",
    tester: "you are Tester",
    packageSafe: "Your Package is safe",
    minimumPackage: "minimumPackage",
    thisusernotunblock: "This user not unblock ",
    noChangesDone: "No changes done !",
    thisusernotblock: "This user not block ",
    YourInteresthasbeendeleted: "Your interest has been deleted",
    interestSafe: "Your interest is safe",
    test: "test",
    test: "test",
    test: "test",
    test: "test",
    test: "test",
};
