﻿﻿@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-RegularItalic.eot");
	src: local("Scandia Regular Italic"), local("Scandia-RegularItalic"),
		url("../font/scandia/Scandia-RegularItalic.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-RegularItalic.woff2") format("woff2"),
		url("../font/scandia/Scandia-RegularItalic.woff") format("woff"),
		url("../font/scandia/Scandia-RegularItalic.ttf") format("truetype");
	font-weight: normal;
	font-style: italic;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-Light.eot");
	src: local("Scandia Light"), local("Scandia-Light"),
		url("../font/scandia/Scandia-Light.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-Light.woff2") format("woff2"),
		url("../font/scandia/Scandia-Light.woff") format("woff"),
		url("../font/scandia/Scandia-Light.ttf") format("truetype");
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-BoldItalic.eot");
	src: local("Scandia Bold Italic"), local("Scandia-BoldItalic"),
		url("../font/scandia/Scandia-BoldItalic.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-BoldItalic.woff2") format("woff2"),
		url("../font/scandia/Scandia-BoldItalic.woff") format("woff"),
		url("../font/scandia/Scandia-BoldItalic.ttf") format("truetype");
	font-weight: bold;
	font-style: italic;
}

@font-face {
	font-family: "Scandia Line";
	src: url("../font/scandia/ScandiaLine-Bold.eot");
	src: local("Scandia Line Bold"), local("ScandiaLine-Bold"),
		url("../font/scandia/ScandiaLine-Bold.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/ScandiaLine-Bold.woff2") format("woff2"),
		url("../font/scandia/ScandiaLine-Bold.woff") format("woff"),
		url("../font/scandia/ScandiaLine-Bold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal;
}

@font-face {
	font-family: "Scandia Line";
	src: url("../font/scandia/ScandiaLine-Light.eot");
	src: local("Scandia Line Light"), local("ScandiaLine-Light"),
		url("../font/scandia/ScandiaLine-Light.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/ScandiaLine-Light.woff2") format("woff2"),
		url("../font/scandia/ScandiaLine-Light.woff") format("woff"),
		url("../font/scandia/ScandiaLine-Light.ttf") format("truetype");
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: "Scandia Stencil";
	src: url("../font/scandia/Scandia-Stencil.eot");
	src: local("Scandia Stencil"), local("Scandia-Stencil"),
		url("../font/scandia/Scandia-Stencil.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-Stencil.woff2") format("woff2"),
		url("../font/scandia/Scandia-Stencil.woff") format("woff"),
		url("../font/scandia/Scandia-Stencil.ttf") format("truetype");
	font-weight: 900;
	font-style: normal;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-Medium.eot");
	src: local("Scandia Medium"), local("Scandia-Medium"),
		url("../font/scandia/Scandia-Medium.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-Medium.woff2") format("woff2"),
		url("../font/scandia/Scandia-Medium.woff") format("woff"),
		url("../font/scandia/Scandia-Medium.ttf") format("truetype");
	font-weight: 500;
	font-style: normal;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-LightItalic.eot");
	src: local("Scandia Light Italic"), local("Scandia-LightItalic"),
		url("../font/scandia/Scandia-LightItalic.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-LightItalic.woff2") format("woff2"),
		url("../font/scandia/Scandia-LightItalic.woff") format("woff"),
		url("../font/scandia/Scandia-LightItalic.ttf") format("truetype");
	font-weight: 300;
	font-style: italic;
}

@font-face {
	font-family: "Scandia Line";
	src: url("../font/scandia/ScandiaLine-Medium.eot");
	src: local("Scandia Line Medium"), local("ScandiaLine-Medium"),
		url("../font/scandia/ScandiaLine-Medium.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/ScandiaLine-Medium.woff2") format("woff2"),
		url("../font/scandia/ScandiaLine-Medium.woff") format("woff"),
		url("../font/scandia/ScandiaLine-Medium.ttf") format("truetype");
	font-weight: 500;
	font-style: normal;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-MediumItalic.eot");
	src: local("Scandia Medium Italic"), local("Scandia-MediumItalic"),
		url("../font/scandia/Scandia-MediumItalic.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-MediumItalic.woff2") format("woff2"),
		url("../font/scandia/Scandia-MediumItalic.woff") format("woff"),
		url("../font/scandia/Scandia-MediumItalic.ttf") format("truetype");
	font-weight: 500;
	font-style: italic;
}

@font-face {
	font-family: "Scandia Line Stencil";
	src: url("../font/scandia/ScandiaLine-Stencil.eot");
	src: local("Scandia Line Stencil"), local("ScandiaLine-Stencil"),
		url("../font/scandia/ScandiaLine-Stencil.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/ScandiaLine-Stencil.woff2") format("woff2"),
		url("../font/scandia/ScandiaLine-Stencil.woff") format("woff"),
		url("../font/scandia/ScandiaLine-Stencil.ttf") format("truetype");
	font-weight: 900;
	font-style: normal;
}

@font-face {
	font-family: "Scandia Line";
	src: url("../font/scandia/ScandiaLine-Regular.eot");
	src: local("Scandia Line Regular"), local("ScandiaLine-Regular"),
		url("../font/scandia/ScandiaLine-Regular.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/ScandiaLine-Regular.woff2") format("woff2"),
		url("../font/scandia/ScandiaLine-Regular.woff") format("woff"),
		url("../font/scandia/ScandiaLine-Regular.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-Bold.eot");
	src: local("Scandia Bold"), local("Scandia-Bold"),
		url("../font/scandia/Scandia-Bold.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-Bold.woff2") format("woff2"),
		url("../font/scandia/Scandia-Bold.woff") format("woff"),
		url("../font/scandia/Scandia-Bold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal;
}

@font-face {
	font-family: "Scandia";
	src: url("../font/scandia/Scandia-Regular.eot");
	src: local("Scandia Regular"), local("Scandia-Regular"),
		url("../font/scandia/Scandia-Regular.eot?#iefix") format("embedded-opentype"),
		url("../font/scandia/Scandia-Regular.woff2") format("woff2"),
		url("../font/scandia/Scandia-Regular.woff") format("woff"),
		url("../font/scandia/Scandia-Regular.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
}
:root {
	--theme-color: linear-gradient(to bottom, #FF6F43 0%, #FE1B03 100%);
	--theme-color-light: #ff7e70;
	/* --theme-color: #ce693e;
	--theme-color-light: #e7855b; */
	--bg-white: #fff;
	--text-white: #fff;
	--black: #141010;
	--theme-active-text-color: #141010;
	--border-radius: 30px;
}
body::-webkit-scrollbar {
	width: 8px;
	padding: 10px;
	margin: 10px;
}

body::-webkit-scrollbar-track {
	background: #f2f2f2;
	padding: 10px;
}
ul,
ul li {
	padding: 0;
	margin: 0;
}

[data-tooltip] {
	--arrow-size: 5px;
	position: relative;
	z-index: 10;
  }
  
  /* Positioning and visibility settings of the tooltip */
  [data-tooltip]:before,
  [data-tooltip]:after {
	position: absolute;
	visibility: hidden;
	opacity: 0;
	left: 50%;
	bottom: calc(100% + var(--arrow-size));
	pointer-events: none;
	transition: 0.2s;
	will-change: transform;
  }
  
  /* The actual tooltip with a dynamic width */
  [data-tooltip]:before {
	content: attr(data-tooltip);
	padding: 8px 18px;
	min-width: 50px;
	max-width: 300px;
	font-weight: 400px;
	width: max-content;
	width: -moz-max-content;
	border-radius: 6px;
	font-size: 14px;
	background: rgba(59, 72, 80, 0.9);
	background-image: linear-gradient(30deg,
	  rgba(59, 72, 80, 0.44),
	  rgba(59, 68, 75, 0.44),
	  rgba(60, 82, 88, 0.44));
	box-shadow: 0px 0px 24px rgba(0, 0, 0, 0.2);
	color: #fff;
	text-align: center;
	white-space: pre-wrap;
	transform: translate(-50%,  calc(0px - var(--arrow-size))) scale(0.5);
  }
  
  /* Tooltip arrow */
  [data-tooltip]:after {
	content: '';
	border-style: solid;
	border-width: var(--arrow-size) var(--arrow-size) 0px var(--arrow-size); /* CSS triangle */
	border-color: rgba(55, 64, 70, 0.9) transparent transparent transparent;
	transition-duration: 0s; /* If the mouse leaves the element, 
								the transition effects for the 
								tooltip arrow are "turned off" */
	transform-origin: top;   /* Orientation setting for the
								slide-down effect */
	transform: translateX(-50%) scaleY(0);
  }
  
  /* Tooltip becomes visible at hover */
  [data-tooltip]:hover:before,
  [data-tooltip]:hover:after {
	visibility: visible;
	opacity: 1;
  }
  /* Scales from 0.5 to 1 -> grow effect */
  [data-tooltip]:hover:before {
	transition-delay: 0.3s;
	transform: translate(-50%, calc(0px - var(--arrow-size))) scale(1);
  }
  /* 
	Arrow slide down effect only on mouseenter (NOT on mouseleave)
  */
  [data-tooltip]:hover:after {
	transition-delay: 0.5s; /* Starting after the grow effect */
	transition-duration: 0.2s;
	transform: translateX(-50%) scaleY(1);
  }

body::-webkit-scrollbar-thumb {
	background: #cdcdcd;
	border-radius: 10px;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-ms-border-radius: 10px;
	-o-border-radius: 10px;
}

body {
	background: var(--bg-white);
	font-size: 14px;
	font-weight: 400;
	font-family: "Scandia";
	color: var(--black);
}

.theme-bg {
	background: var(--theme-color);
}

.btn.theme-btn {
	background: var(--theme-color);
	/* filter: drop-shadow(0px 4px 11px var(--theme-color-light));
  -webkit-filter: drop-shadow(0px 4px 11px var(--theme-color-light)); */
	transition: 0.5s all ease-in-out;
	-webkit-transition: 0.5s all ease-in-out;
	-moz-transition: 0.5s all ease-in-out;
	-ms-transition: 0.5s all ease-in-out;
	-o-transition: 0.5s all ease-in-out;
	font-size: 14px;
}

.btn.theme-btn:hover {
	background: var(--theme-color) !important;
	/* filter: drop-shadow(0px 4px 11px var(--theme-color-light));
  -webkit-filter: drop-shadow(0px 4px 11px var(--theme-color-light)); */
	transform: translateY(-5px);
	-webkit-transform: translateY(-5px);
	-moz-transform: translateY(-5px);
	-ms-transform: translateY(-5px);
	-o-transform: translateY(-5px);
}
.primary-color {
	color: var(--theme-color-light);
}
.padding-0 {
	padding: 0;
}

.font-6 {
	font-size: 6px !important;
}

.font-7 {
	font-size: 7px !important;
}

.font-8 {
	font-size: 8px !important;
}

.font-9 {
	font-size: 9px !important;
}

.font-10 {
	font-size: 10px !important;
}

.font-11 {
	font-size: 11px !important;
}

.font-12 {
	font-size: 12px !important;
}

.font-13 {
	font-size: 13px !important;
}

.font-14 {
	font-size: 14px !important;
}

.font-15 {
	font-size: 15px !important;
}

.font-16 {
	font-size: 16px !important;
}

.font-17 {
	font-size: 17px !important;
}

.font-18 {
	font-size: 18px !important;
}

.font-19 {
	font-size: 19px !important;
}

.font-20 {
	font-size: 20px !important;
}

.font-21 {
	font-size: 21px !important;
}

.font-22 {
	font-size: 22px !important;
}

.font-23 {
	font-size: 23px !important;
}

.font-24 {
	font-size: 24px !important;
}

.font-25 {
	font-size: 25px !important;
}

.font-26 {
	font-size: 26px !important;
}

.font-27 {
	font-size: 27px !important;
}

.font-28 {
	font-size: 28px !important;
}

.font-29 {
	font-size: 29px !important;
}

.font-30 {
	font-size: 30px !important;
}

.font-31 {
	font-size: 31px !important;
}

.font-32 {
	font-size: 32px !important;
}

.font-33 {
	font-size: 33px !important;
}

.font-34 {
	font-size: 34px !important;
}

.font-35 {
	font-size: 35px !important;
}

.font-36 {
	font-size: 36px !important;
}

.font-37 {
	font-size: 37px !important;
}

.font-38 {
	font-size: 38px !important;
}

.font-39 {
	font-size: 39px !important;
}

.font-40 {
	font-size: 40px !important;
}

.font-41 {
	font-size: 41px !important;
}

.font-42 {
	font-size: 42px !important;
}

.font-43 {
	font-size: 43px !important;
}

.font-44 {
	font-size: 44px !important;
}

.font-45 {
	font-size: 45px !important;
}

.font-46 {
	font-size: 46px !important;
}

.font-47 {
	font-size: 47px !important;
}

.font-48 {
	font-size: 48px !important;
}

.font-49 {
	font-size: 49px !important;
}

.font-50 {
	font-size: 50px !important;
}

.pull-left {
	float: left;
}

.pull-right {
	float: right;
}

.align-left {
	text-align: left;
}

.align-center {
	text-align: center;
}

.align-right {
	text-align: right;
}

.align-justify {
	text-align: justify;
}

.no-resize {
	resize: none;
}

.font-bold {
	font-weight: bold;
}

.font-italic {
	font-style: italic;
}

.font-underline {
	text-decoration: underline;
}

.font-line-through {
	text-decoration: line-through;
}

.font-overline {
	text-decoration: overline;
}

.bg-red {
	background: #f44336 !important;
	color: var(--text-white);
}

.bg-red .content .text,
.bg-red .content .number {
	color: var(--text-white);
}

.bg-pink {
	background: #e91e63 !important;
	color: var(--text-white);
}

.bg-pink .content .text,
.bg-pink .content .number {
	color: var(--text-white);
}

.bg-purple {
	background: #9c27b0 !important;
	color: var(--text-white);
}

.bg-purple .content .text,
.bg-purple .content .number {
	color: var(--text-white);
}

.bg-deep-purple {
	background: #673ab7 !important;
	color: var(--text-white);
}

.bg-deep-purple .content .text,
.bg-deep-purple .content .number {
	color: var(--text-white);
}

.bg-indigo {
	background: var(--theme-color) !important;
	color: var(--text-white);
}

.bg-indigo .content .text,
.bg-indigo .content .number {
	color: var(--text-white);
}

.bg-blue {
	background: #2196f3 !important;
	color: var(--text-white);
}

.bg-blue .content .text,
.bg-blue .content .number {
	color: var(--text-white);
}

.bg-light-blue {
	background: #03a9f4 !important;
	color: var(--text-white);
}

.bg-light-blue .content .text,
.bg-light-blue .content .number {
	color: var(--text-white);
}

.bg-cyan {
	background: #29c0b1 !important;
	color: var(--text-white);
}

.bg-cyan .content .text,
.bg-cyan .content .number {
	color: var(--text-white);
}

.bg-teal {
	background: #009688 !important;
	color: var(--text-white);
}

.bg-teal .content .text,
.bg-teal .content .number {
	color: var(--text-white);
}

.bg-green {
	background: #4caf50 !important;
	color: var(--text-white);
}

.bg-green .content .text,
.bg-green .content .number {
	color: var(--text-white);
}

.bg-light-green {
	background: #8bc34a !important;
	color: var(--text-white);
}

.bg-light-green .content .text,
.bg-light-green .content .number {
	color: var(--text-white);
}

.bg-lime {
	background: #cddc39 !important;
	color: var(--text-white);
}

.bg-lime .content .text,
.bg-lime .content .number {
	color: var(--text-white);
}

.bg-yellow {
	background: #ffe821 !important;
	color: var(--text-white);
}

.bg-yellow .content .text,
.bg-yellow .content .number {
	color: var(--text-white);
}

.bg-amber {
	background: #ffc107 !important;
	color: var(--text-white);
}

.bg-amber .content .text,
.bg-amber .content .number {
	color: var(--text-white);
}

.bg-orange {
	background: #ff9800 !important;
	color: var(--text-white);
}

.bg-orange .content .text,
.bg-orange .content .number {
	color: var(--text-white);
}

.bg-deep-orange {
	background: #ff5722 !important;
	color: var(--text-white);
}

.bg-deep-orange .content .text,
.bg-deep-orange .content .number {
	color: var(--text-white);
}

.bg-brown {
	background: #795548 !important;
	color: var(--text-white);
}

.bg-brown .content .text,
.bg-brown .content .number {
	color: var(--text-white);
}

.bg-grey {
	background: #9e9e9e !important;
	color: var(--text-white);
}

.bg-grey .content .text,
.bg-grey .content .number {
	color: var(--text-white);
}

.bg-blue-grey {
	background: #607d8b !important;
	color: var(--text-white);
}

.bg-blue-grey .content .text,
.bg-blue-grey .content .number {
	color: var(--text-white);
}

.bg-black {
	background: var(--black) !important;
	color: var(--text-white);
}

.bg-black .content .text,
.bg-black .content .number {
	color: var(--text-white);
}

.bg-white {
	background: var(--bg-white) !important;
	color: var(--text-white);
}

.bg-white .content .text,
.bg-white .content .number {
	color: var(--text-white);
}

.btn:focus,
.btn:active,
.btn:active:focus,
.custom-select:focus,
.form-control:focus {
	box-shadow: none !important;
	outline: none;
}

a {
	color: var(--theme-color);
	font-weight: 500;
	transition: all 0.5s;
	-webkit-transition: all 0.5s;
	-o-transition: all 0.5s;
}

a:not(.btn-social-icon):not(.btn-social):not(.page-link) .ion,
a:not(.btn-social-icon):not(.btn-social):not(.page-link) .fas,
a:not(.btn-social-icon):not(.btn-social):not(.page-link) .far,
a:not(.btn-social-icon):not(.btn-social):not(.page-link) .fal,
a:not(.btn-social-icon):not(.btn-social):not(.page-link) .fab {
	margin-left: 4px;
}

.bg-primary {
	background: var(--theme-color) !important;
}

.bg-secondary {
	background: #cdd3d8 !important;
}

.bg-success {
	background: #54ca68 !important;
}

.bg-info {
	background: #3abaf4 !important;
}

.bg-warning {
	background: #ffa426 !important;
}

.bg-danger {
	background: #fc544b !important;
}

.bg-light {
	background: #e3eaef !important;
}

.bg-dark {
	background: #191d21 !important;
}

.text-primary,
.text-primary-all *,
.text-primary-all *:before,
.text-primary-all *:after {
	color: var(--theme-color) !important;
}

.text-secondary,
.text-secondary-all *,
.text-secondary-all *:before,
.text-secondary-all *:after {
	color: #cdd3d8 !important;
}

.text-success,
.text-success-all *,
.text-success-all *:before,
.text-success-all *:after {
	color: #54ca68 !important;
}

.text-info,
.text-info-all *,
.text-info-all *:before,
.text-info-all *:after {
	color: #3abaf4 !important;
}

.text-warning,
.text-warning-all *,
.text-warning-all *:before,
.text-warning-all *:after {
	color: #ffa426 !important;
}

.text-danger,
.text-danger-all *,
.text-danger-all *:before,
.text-danger-all *:after {
	color: #fc544b !important;
}

.text-light,
.text-light-all *,
.text-light-all *:before,
.text-light-all *:after {
	color: #e3eaef !important;
}

.text-white,
.text-white-all *,
.text-white-all *:before,
.text-white-all *:after {
	color: var(--text-white);
}

.text-dark,
.text-dark-all *,
.text-dark-all *:before,
.text-dark-all *:after {
	color: #191d21 !important;
}

.fw-normal {
	font-weight: 500 !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: 600;
}

p,
ul:not(.list-unstyled),
ol {
	line-height: 28px;
}

.shadow {
	box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
}

.text-muted {
	color: #98a6ad !important;
}

.form-control,
.input-group-text,
.custom-select,
.custom-file-label {
	background: #fdfdff;
	border-color: #e4e6fc;
}

.form-control:focus,
.input-group-text:focus,
.custom-select:focus,
.custom-file-label:focus {
	background: #fefeff;
	border-color: var(--theme-color);
}

.input-group-text,
select.form-control:not([size]):not([multiple]),
.form-control:not(.form-control-sm):not(.form-control-lg) {
	font-size: 18px;
	padding: 5px 25px;
	height: 50px;
}

textarea.form-control {
	height: 100px !important;
}

.custom-control {
	line-height: 1.6rem;
}

.custom-file,
.custom-file-label,
.custom-select,
.custom-file-label:after,
.form-control[type="color"] {
	height: calc(2.25rem + 6px);
}
select.form-control:not([size]):not([multiple]) {
	height: 50px;
}

.form-group .control-label,
.form-group>label {
	font-weight: 500;
	color: var(--black);
	font-size: 16px;
	letter-spacing: 0.5px;
}

.form-label {
	margin-bottom: 0.4rem;
}

.form-control {
	border-radius: 30px;
	padding: 6px 30px !important;
	height: 45px;
	background: var(--text-white);
}

.input-group-append [class*="btn-outline-"] {
	background: #fdfdff;
}

.form-text {
	font-size: 12px;
	line-height: 22px;
}

.custom-radio .custom-control-input:checked~.custom-control-label::before,
.custom-control-input:checked~.custom-control-label::before {
	background: var(--theme-color) !important;
}

.custom-file-label {
	line-height: 2.2;
}

.custom-file-label:after {
	height: calc(2.25rem + 4px);
	line-height: 2.2;
	border-color: transparent;
}

.custom-file-label:focus,
.custom-file-label:active {
	box-shadow: none;
	outline: none;
}

.custom-file-input:focus+.custom-file-label {
	box-shadow: none;
	border-color: var(--theme-color);
}

.custom-file-input:focus+.custom-file-label:after {
	border-color: transparent;
}

.selectgroup {
	display: -ms-inline-flexbox;
	display: inline-flex;
}

.selectgroup-item {
	-ms-flex-positive: 1;
	flex-grow: 1;
	position: relative;
}

.selectgroup-item+.selectgroup-item {
	margin-left: -1px;
}

.selectgroup-item:not(:first-child) .selectgroup-button {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}

.selectgroup-item:not(:last-child) .selectgroup-button {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}

.selectgroup-input {
	opacity: 0;
	position: absolute;
	z-index: -1;
	top: 0;
	left: 0;
}

.selectgroup-input-radio {
	opacity: 0;
	position: absolute;
	z-index: -1;
	top: 0;
	left: 0;
}

.selectgroup-button {
	background: #fdfdff;
	border-color: #e4e6fc;
	border-width: 1px;
	border-style: solid;
	display: block;
	text-align: center;
	padding: 0 1rem;
	height: 35px;
	position: relative;
	cursor: pointer;
	border-radius: 3px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	font-size: 13px;
	min-width: 2.375rem;
	line-height: 36px;
}

.selectgroup-button-icon {
	padding-left: 0.5rem;
	padding-right: 0.5rem;
}

.selectgroup-button-icon i {
	font-size: 14px;
}

.selectgroup-input-radio:focus+.selectgroup-button,
.selectgroup-input-radio:checked+.selectgroup-button {
	background: var(--theme-color);
	color: var(--text-white);
	z-index: 1;
}

.selectgroup-pills {
	display: block;
	flex-wrap: wrap;
	align-items: flex-start;
}

.selectgroup-pills .selectgroup-item {
	margin-right: 0.5rem;
	flex-grow: 0;
}

.selectgroup-pills .selectgroup-button {
	border-radius: 50px !important;
}

.selectgroup-pills input:checked+span {
	background: var(--theme-color);
	border-radius: 50px !important;
	color: var(--text-white);
	z-index: 1;
}

.custom-switch {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	cursor: default;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-ms-flex-align: center;
	align-items: center;
	margin: 0;
}

.custom-switch-input {
	position: absolute;
	z-index: -1;
	opacity: 0;
}

.custom-switches-stacked {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-direction: column;
	flex-direction: column;
}

.custom-switches-stacked .custom-switch {
	margin-bottom: 0.5rem;
}

.custom-switch-indicator {
	display: inline-block;
	height: 1.25rem;
	width: 2.25rem;
	background: #e9ecef;
	border-radius: 50px;
	position: relative;
	vertical-align: bottom;
	border: 1px solid rgba(0, 40, 100, 0.12);
	transition: 0.3s border-color, 0.3s background;
}

.custom-switch-indicator:before {
	content: "";
	position: absolute;
	height: calc(1.25rem - 4px);
	width: calc(1.25rem - 4px);
	top: 1px;
	left: 1px;
	background: var(--text-white);
	border-radius: 50%;
	transition: 0.3s left;
}

.custom-switch-input:checked~.custom-switch-indicator {
	background: var(--theme-color);
}

.custom-switch-input:checked~.custom-switch-indicator:before {
	left: calc(1rem + 1px);
}

.custom-switch-input:focus~.custom-switch-indicator {
	border-color: var(--theme-color);
}

.custom-switch-description {
	margin-left: 0.5rem;
	color: #6e7687;
	transition: 0.3s color;
}

.custom-switch-input:checked~.custom-switch-description {
	color: #495057;
}

.colorinput {
	margin: 0;
	position: relative;
	cursor: pointer;
}

.colorinput-input {
	position: absolute;
	z-index: -1;
	opacity: 0;
}

.colorinput-color {
	background: #fdfdff;
	border-color: #e4e6fc;
	border-width: 1px;
	border-style: solid;
	display: inline-block;
	width: 1.75rem;
	height: 1.75rem;
	border-radius: 3px;
	color: var(--text-white);
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.colorinput-input:checked~.colorinput-color:before {
	opacity: 1;
}

.list-unstyled-border li {
	border-bottom: 1px solid #f9f9f9;
	padding-bottom: 15px;
	margin-bottom: 10px;
}

.list-unstyled-border li .custom-checkbox {
	margin-right: 15px;
}

.list-unstyled-border li:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.list-unstyled-noborder li:last-child {
	border-bottom: none;
}

.list-group-item.active {
	background: var(--theme-color);
}

.list-group-item.disabled {
	color: #c9d7e0;
}

.list-group-item-primary {
	background: var(--theme-color);
	color: var(--text-white);
}

.list-group-item-secondary {
	background: #cdd3d8;
	color: var(--text-white);
}

.list-group-item-success {
	background: #54ca68;
	color: var(--text-white);
}

.list-group-item-danger {
	background: #fc544b;
	color: var(--text-white);
}

.list-group-item-warning {
	background: #ffa426;
	color: var(--text-white);
}

.list-group-item-info {
	background: #3abaf4;
	color: var(--text-white);
}

.list-group-item-light {
	background: #e3eaef;
	color: #191d21;
}

.list-group-item-dark {
	background: #191d21;
	color: var(--text-white);
}

.alert {
	color: var(--text-white);
	border: none;
	padding: 15px 20px;
}

.alert .alert-title {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 5px;
}

.alert code {
	background: var(--bg-white);
	border-radius: 3px;
	padding: 1px 4px;
}

.alert p {
	margin-bottom: 0;
}

.alert.alert-has-icon {
	display: flex;
}

.alert.alert-has-icon .alert-icon {
	margin-top: 4px;
	width: 30px;
}

.alert.alert-has-icon .alert-icon .ion,
.alert.alert-has-icon .alert-icon .fas,
.alert.alert-has-icon .alert-icon .far,
.alert.alert-has-icon .alert-icon .fab,
.alert.alert-has-icon .alert-icon .fal {
	font-size: 20px;
}

.alert.alert-has-icon .alert-body {
	flex: 1;
}

.alert:not(.alert-light) a {
	color: var(--text-white);
}

.alert.alert-primary {
	background: var(--theme-color);
}

.alert.alert-secondary {
	background: #cdd3d8;
}

.alert.alert-success {
	background: #54ca68;
}

.alert.alert-info {
	background: #3abaf4;
}

.alert.alert-warning {
	background: #ffa426;
}

.alert.alert-danger {
	background: #fc544b;
}

.alert.alert-light {
	background: #e3eaef;
	color: #191d21;
}

.alert.alert-dark {
	background: #191d21;
}

.card {
	background: var(--bg-white);
	border: none;
	position: relative;
	margin-bottom: 30px;
	box-shadow: 0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1), 0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1), 0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12), 0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-ms-border-radius: 20px;
	-o-border-radius: 20px;
}

.card .card-header,
.card .card-body,
.card .card-footer {
	background: transparent;
	padding: 20px 25px;
}
.card .navbar {
	position: static;
}

.card .card-body {
	padding-top: 20px;
	padding-bottom: 20px;
}

.card .card-body .section-title {
	margin: 30px 0 10px 0;
	font-size: 16px;
}

.card .card-body .section-title:before {
	margin-top: 8px;
}

.card .card-body .section-title+.section-lead {
	margin-top: -5px;
}

.card .card-body p {
	font-weight: 500;
	color: #212529;
}

.card .card-header {
	border-bottom: 2px solid #ebebeb;
	line-height: 30px;
	-ms-grid-row-align: center;
	align-self: center;
	width: 100%;
	padding: 20px 25px;
	display: flex;
	align-items: center;
}

.card .card-header .form-control {
	height: 31px;
	font-size: 13px;
	border-radius: 30px;
}

.card .card-header .form-control+.input-group-btn .btn {
	margin-top: -1px;
}

.card .card-header h4 {
	font-size: 17px;
	line-height: 28px;
	padding-right: 10px;
	margin-bottom: 0;
	color: #212529;
}

.card .card-header h4+.card-header-action,
.card .card-header h4+.card-header-form {
	margin-left: auto;
}

.card .card-header h4+.card-header-action .btn,
.card .card-header h4+.card-header-form .btn {
	font-size: 12px;
	border-radius: 30px !important;
	padding-left: 13px !important;
	padding-right: 13px !important;
}

.card .card-header h4+.card-header-action .btn.active,
.card .card-header h4+.card-header-form .btn.active {
	/* box-shadow: 0 2px 6px var(--theme-color-light); */
	background: var(--theme-color);
	color: var(--text-white);
}

.card .card-header h4+.card-header-action .dropdown,
.card .card-header h4+.card-header-form .dropdown {
	display: inline;
}

.card .card-header h4+.card-header-action .btn-group .btn,
.card .card-header h4+.card-header-form .btn-group .btn {
	border-radius: 0 !important;
}

.card .card-header h4+.card-header-action .btn-group .btn:first-child,
.card .card-header h4+.card-header-form .btn-group .btn:first-child {
	border-radius: 30px 0 0 30px !important;
}

.card .card-header h4+.card-header-action .btn-group .btn:last-child,
.card .card-header h4+.card-header-form .btn-group .btn:last-child {
	border-radius: 0 30px 30px 0 !important;
}

.card .card-header h4+.card-header-action .input-group .form-control,
.card .card-header h4+.card-header-form .input-group .form-control {
	border-radius: 30px 0 0 30px !important;
}

.card .card-header h4+.card-header-action .input-group .form-control+.input-group-btn .btn,
.card .card-header h4+.card-header-form .input-group .form-control+.input-group-btn .btn {
	border-radius: 0 30px 30px 0 !important;
}

.card .card-header h4+.card-header-action .input-group .input-group-btn+.form-control,
.card .card-header h4+.card-header-form .input-group .input-group-btn+.form-control {
	border-radius: 0 30px 30px 0 !important;
}

.card .card-header h4+.card-header-action .input-group .input-group-btn .btn,
.card .card-header h4+.card-header-form .input-group .input-group-btn .btn {
	margin-top: -1px;
	border-radius: 30px 0 0 30px !important;
}

.card .card-footer {
	background: transparent;
	border: none;
}

.card.card-mt {
	margin-top: 30px;
}

.card.card-progress:after {
	content: " ";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(255, 255, 255, 0.5);
	z-index: 99;
	z-index: 99;
}

.card.card-progress .card-progress-dismiss {
	position: absolute;
	top: 66%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	z-index: 999;
	color: var(--text-white);
	padding: 5px 13px;
}

.card.card-progress.remove-spinner .card-progress-dismiss {
	top: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.card.card-progress:not(.remove-spinner):after {
	background-image: url("../img/spinner.svg");
	background-size: 80px;
	background-repeat: no-repeat;
	background-position: center;
}

.card.card-primary {
	border-top: 2px solid var(--theme-color);
}

.card.card-secondary {
	border-top: 2px solid #34395e;
}

.card.card-success {
	border-top: 2px solid #54ca68;
}

.card.card-danger {
	border-top: 2px solid #fc544b;
}

.card.card-warning {
	border-top: 2px solid #ffa426;
}

.card.card-info {
	border-top: 2px solid #3abaf4;
}

.card.card-dark {
	border-top: 2px solid #191d21;
}

.card.card-hero .card-header {
	padding: 40px;
	background-image: linear-gradient(to bottom, var(--theme-color), #95a0f4);
	color: var(--text-white);
	overflow: hidden;
	height: auto;
	min-height: auto;
	display: block;
}

.card.card-hero .card-header h4 {
	font-size: 40px;
	line-height: 1;
}

.card.card-hero .card-header .card-description {
	margin-top: 5px;
	font-size: 16px;
}

.card.card-hero .card-header .card-icon {
	float: right;
	color: #8c98f3;
	margin: -60px;
}

.card.card-hero .card-header .card-icon .ion,
.card.card-hero .card-header .card-icon .fas,
.card.card-hero .card-header .card-icon .far,
.card.card-hero .card-header .card-icon .fab,
.card.card-hero .card-header .card-icon .fal {
	font-size: 140px;
}

.card.card-statistic-1 .card-header,
.card.card-statistic-2 .card-header {
	border-color: transparent;
	padding-bottom: 0;
	height: auto;
	min-height: auto;
	display: block;
}

.card.card-statistic-1 .card-icon {
	width: 30px;
	height: 30px;
	margin: 10px 0px 0px 20px;
	border-radius: 3px;
	line-height: 78px;
	text-align: center;
	float: left;
	font-size: 30px;
}

.card.card-statistic-1 .card-header h4,
.card.card-statistic-2 .card-header h4 {
	line-height: 1.2;
	color: #98a6ad;
}

.card.card-statistic-1 .card-body,
.card.card-statistic-2 .card-body {
	padding-top: 0;
}

.card.card-statistic-1 .card-body,
.card.card-statistic-2 .card-body {
	font-size: 26px;
	font-weight: 700;
	color: #34395e;
	padding-bottom: 0;
}

.card.card-statistic-1,
.card.card-statistic-2 {
	display: inline-block;
	width: 100%;
}

.card.card-statistic-1 .card-icon,
.card.card-statistic-2 .card-icon {
	width: 80px;
	height: 80px;
	margin: 10px;
	border-radius: 3px;
	line-height: 94px;
	text-align: center;
	float: left;
	border-radius: 50px;
	margin-right: 15px;
}

.card.card-statistic-1 .card-icon .ion,
.card.card-statistic-1 .card-icon .fas,
.card.card-statistic-1 .card-icon .far,
.card.card-statistic-1 .card-icon .fab,
.card.card-statistic-1 .card-icon .fal,
.card.card-statistic-2 .card-icon .ion,
.card.card-statistic-2 .card-icon .fas,
.card.card-statistic-2 .card-icon .far,
.card.card-statistic-2 .card-icon .fab,
.card.card-statistic-2 .card-icon .fal {
	font-size: 22px;
	color: var(--text-white);
}

.card.card-statistic-1 .card-icon {
	line-height: 90px;
}

.card.card-statistic-2 .card-icon {
	width: 50px;
	height: 50px;
	line-height: 50px;
	font-size: 22px;
	margin: 25px;
	box-shadow: 5px 3px 10px 0 rgba(21, 15, 15, 0.3);
	border-radius: 10px;
	background: var(--theme-color);
}

.card.card-statistic-1 .card-header,
.card.card-statistic-2 .card-header {
	padding-bottom: 0;
	padding-top: 25px;
}

.card.card-statistic-2 .card-body {
	padding-top: 20px;
}

.card.card-statistic-2 .card-header+.card-body,
.card.card-statistic-2 .card-body+.card-header {
	padding-top: 0;
}

.card.card-statistic-1 .card-header h4,
.card.card-statistic-2 .card-header h4 {
	font-weight: 600;
	font-size: 13px;
	letter-spacing: 0.5px;
}

.card.card-statistic-1 .card-header h4 {
	margin-bottom: 0;
}

.card.card-statistic-2 .card-header h4 {
	text-transform: none;
	margin-bottom: 0;
}

.card.card-statistic-1 .card-body {
	font-size: 20px;
}

.card.card-statistic-2 .card-chart {
	padding-top: 20px;
	margin-left: -9px;
	margin-right: -1px;
	margin-bottom: -15px;
}

.card.card-statistic-2 .card-chart canvas {
	height: 90px !important;
}

.card .card-stats {
	width: 100%;
	display: inline-block;
	margin-top: 2px;
	margin-bottom: -6px;
}

.card .card-stats .card-stats-title {
	padding: 15px 25px;
	background: var(--bg-white);
	font-size: 13px;
	font-weight: 600;
	letter-spacing: 0.3px;
}

.card .card-stats .card-stats-items {
	display: flex;
	height: 50px;
	align-items: center;
}

.card .card-stats .card-stats-item {
	width: calc(100% / 3);
	text-align: center;
	padding: 5px 20px;
}

.card .card-stats .card-stats-item .card-stats-item-label {
	font-size: 12px;
	letter-spacing: 0.5px;
	margin-top: 4px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.card .card-stats .card-stats-item .card-stats-item-count {
	line-height: 1;
	margin-bottom: 8px;
	font-size: 20px;
	font-weight: 700;
}

.card.card-large-icons {
	display: flex;
	flex-direction: row;
}

.card.card-large-icons .card-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	width: 150px;
	border-radius: 3px 0 0 3px;
}

.card.card-large-icons .card-icon .ion,
.card.card-large-icons .card-icon .fas,
.card.card-large-icons .card-icon .far,
.card.card-large-icons .card-icon .fab,
.card.card-large-icons .card-icon .fal {
	font-size: 60px;
}

.card.card-large-icons .card-body {
	padding: 25px 30px;
}

.card.card-large-icons .card-body h4 {
	font-size: 18px;
}

.card.card-large-icons .card-body p {
	opacity: 0.6;
	font-weight: 500;
}

.card.card-large-icons .card-body a.card-cta {
	text-decoration: none;
}

.card.card-large-icons .card-body a.card-cta i {
	margin-left: 7px;
}

.card.bg-primary,
.card.bg-danger,
.card.bg-success,
.card.bg-info,
.card.bg-dark,
.card.bg-warning {
	color: var(--text-white);
}

.card.bg-primary .card-header,
.card.bg-danger .card-header,
.card.bg-success .card-header,
.card.bg-info .card-header,
.card.bg-dark .card-header,
.card.bg-warning .card-header {
	color: var(--text-white);
	opacity: 0.9;
}

.card .card-type-3 .card-circle {
	display: inline-flex;
	text-align: center;
	border-radius: 50%;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	height: 45px;
	width: 45px;
	box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.14),
		0 7px 10px -5px rgba(76, 175, 80, 0.4);
}

.card .card-type-3 .card-circle i {
	font-size: 15px;
}

.card .card-statistic-3 {
	position: relative;
	color: var(--text-white);
	padding: 15px;
	border-radius: 3px;
	overflow: hidden;
}

.card .card-statistic-3 .card-icon-large {
	font-size: 110px;
	width: 110px;
	height: 50px;
	text-shadow: 3px 7px rgba(0, 0, 0, 0.3);
}

.card .card-statistic-3 .card-icon {
	text-align: center;
	line-height: 50px;
	margin-left: 15px;
	color: var(--black);
	position: absolute;
	right: -5px;
	top: 20px;
	opacity: 0.1;
}

.card .card-statistic-3 .banner-img img {
	max-width: 100%;
}

.card .card-statistic-4 {
	position: relative;
	color: var(--black);
	padding: 15px;
	border-radius: 3px;
	overflow: hidden;
}

.card .card-statistic-4 .card-icon-large {
	font-size: 110px;
	width: 110px;
	height: 50px;
	text-shadow: 3px 7px rgba(0, 0, 0, 0.3);
}

.card .card-statistic-4 .card-icon {
	text-align: center;
	line-height: 50px;
	margin-left: 15px;
	color: var(--black);
	position: absolute;
	right: -5px;
	top: 20px;
	opacity: 0.1;
}

.card .card-statistic-4 .banner-img img {
	max-width: 100%;
	float: right;
}

@media (max-width: 575.98px) {
	.card.card-large-icons {
		display: inline-block;
	}

	.card.card-large-icons .card-icon {
		width: 100%;
		height: 200px;
	}

	.col-xs-6 {
		-ms-flex: 0 0 50%;
		-webkit-box-flex: 0;
		flex: 0 0 50%;
		max-width: 50%;
	}
}

@media (max-width: 767.98px) {
	.card .card-header {
		height: auto;
		flex-wrap: wrap;
	}

	.card .card-header h4+.card-header-action,
	.card .card-header h4+.card-header-form {
		flex-grow: 0;
		width: 100%;
		margin-top: 10px;
	}
}

@media (min-width: 768px) and (max-width: 991.98px) {
	.card .card-stats .card-stats-items {
		height: 49px;
	}

	.card .card-stats .card-stats-items .card-stats-item {
		padding: 5px 7px;
	}

	.card .card-stats .card-stats-items .card-stats-item .card-stats-item-count {
		font-size: 16px;
	}

	.card.card-sm-6 .card-chart canvas {
		height: 85px !important;
	}

	.card.card-hero .card-header {
		padding: 25px;
	}
}

.table td,
.table:not(.table-bordered) th {
	border-top: none;
}

.table:not(.table-sm):not(.table-md):not(.dataTable) td,
.table:not(.table-sm):not(.table-md):not(.dataTable) th {
	padding: 0 10px;
	height: 60px;
	vertical-align: middle;
}

.table:not(.table-sm) thead th {
	border-bottom: none;
	background: rgb(251 251 251);
	background: #a9a9a926;
	color: var(--black);
	padding-top: 15px;
	padding-bottom: 15px;
	font-size: 14px;
}

.table.table-md th,
.table.table-md td {
	padding: 10px 15px;
}

.table.table-bordered td,
.table.table-bordered th {
	border-color: #f6f6f6;
}

.table .team-member {
	position: relative;
	width: 30px;
	white-space: nowrap;
	border-radius: 1000px;
	vertical-align: bottom;
	display: inline-block;
}

.table .team-member img {
	width: 100%;
	max-width: 100%;
	height: auto;
	border: 0;
	border-radius: 1000px;
}

.table .team-member-sm {
	width: 32px;
	-webkit-transition: all 0.25s ease;
	-o-transition: all 0.25s ease;
	-moz-transition: all 0.25s ease;
	transition: all 0.25s ease;
}

.table .team-member-sm:hover {
	webkit-transform: translateY(-4px) scale(1.02);
	-moz-transform: translateY(-4px) scale(1.02);
	-ms-transform: translateY(-4px) scale(1.02);
	-o-transform: translateY(-4px) scale(1.02);
	transform: translateY(-4px) scale(1.02);
	-webkit-box-shadow: 0 14px 24px rgba(75, 70, 124, 0.2);
	box-shadow: 0 14px 24px rgba(75, 70, 124, 0.2);
	z-index: 999;
}

.table .order-list li img {
	border: 2px solid var(--text-white);
	box-shadow: 4px 3px 6px 0 rgba(0, 0, 0, 0.2);
}

.table .order-list li+li {
	margin-left: -14px;
	background: transparent;
}

.table .order-list li .badge {
	background: rgba(228, 222, 222, 0.8);
	color: #6b6f82;
	margin-bottom: 6px;
}

.table-links {
	color: #34395e;
	font-size: 12px;
	margin-top: 5px;
	opacity: 0;
	transition: all 0.3s;
}

.table-links a {
	color: #666;
}

table tr:hover .table-links {
	opacity: 1;
}

.table-striped tbody tr:nth-of-type(odd) {
	background: rgba(0, 0, 0, 0.02);
}

@media (max-width: 575.98px) {
	.table-responsive table {
		min-width: 800px;
	}
}

.tooltip {
	font-size: 12px;
}

.tooltip-inner {
	padding: 7px 13px;
}

.modal-header,
.modal-body,
.modal-footer {
	padding: 25px;
}

.modal-body {
	padding-top: 15px;
}

.modal-footer {
	padding-top: 15px;
	padding-bottom: 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.modal-header h5 {
	font-size: 18px;
}

.modal-footer {
	border-radius: 0 0 3px 3px;
}

.modal-content {
	max-width: 100%;
	border: none;
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.modal.show .modal-content {
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.modal-progress .modal-content {
	position: relative;
}

.modal-progress .modal-content:after {
	content: " ";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(255, 255, 255, 0.5);
	z-index: 999;
	background-image: url("../img/spinner.svg");
	background-size: 80px;
	background-repeat: no-repeat;
	background-position: center;
	border-radius: 3px;
}

.modal-part {
	display: none;
}

.nav-tabs .nav-item .nav-link {
	color: var(--black);
}

.nav-tabs .nav-item .nav-link.active {
	color: var(--black);
}

.tab-content>.tab-pane {
	/* padding: 10px 0; */
	line-height: 24px;
}

.tab-bordered .tab-pane {
	padding: 15px;
	border: 1px solid #ededed;
	margin-top: -1px;
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
	color: var(--text-white);
	background: var(--theme-color);
}

.nav-pills .nav-item .nav-link {
	color: var(--theme-color);
	padding-left: 15px !important;
	padding-right: 15px !important;
}

.nav-pills .nav-item .nav-link:hover {
	background: #f6f7fe;
}

.nav-pills .nav-item .nav-link.active {
	box-shadow: 0 2px 6px #acb5f6;
	color: var(--text-white);
	background: var(--theme-color);
}

.nav-pills .nav-item .nav-link .badge {
	padding: 5px 8px;
	margin-left: 5px;
}

.nav .nav-item .nav-link .ion,
.nav .nav-item .nav-link .fas,
.nav .nav-item .nav-link .far,
.nav .nav-item .nav-link .fab,
.nav .nav-item .nav-link .fal {
	margin-right: 3px;
	font-size: 12px;
}

.sticky {
	position: fixed !important;
	top: 0;
}

.page-item .page-link {
	color: #727272;
	border-radius: 3px;
	margin: 0 3px;
	-webkit-box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
	box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
}

.page-item.active .page-link {
	background: var(--theme-color);
	border-color: var(--theme-color);
	/* filter: drop-shadow(0px 4px 11px var(--theme-color-light));
  -webkit-filter: drop-shadow(0px 4px 11px var(--theme-color-light)); */
}

.page-item.disabled .page-link {
	border-color: transparent;
	background: #f9fafe;
	color: var(--black);
	opacity: 0.6;
	font-weight: 500;
}

.page-link {
	border-color: transparent;
	background: #f9fafe;
	font-weight: 600;
}

.page-link:hover {
	background: var(--theme-color);
	color: var(--text-white);
	border-color: transparent;
}

.page-link:focus {
	box-shadow: none;
}

.badges .badge {
	margin: 0 8px 10px 0;
}

.badge {
	vertical-align: middle;
	padding: 7px 12px;
	font-weight: 600;
	letter-spacing: 0.3px;
	border-radius: 30px;
	font-size: 12px;
}

.badge.badge-warning {
	color: var(--text-white);
}

.badge.badge-primary {
	background: var(--theme-color);
}

.badge.badge-secondary {
	background: #34395e;
}

.badge.badge-success {
	background: #54ca68;
}

.badge.badge-info {
	background: #3abaf4;
}

.badge.badge-danger {
	background: #fc544b;
}

.badge.badge-light {
	background: #e3eaef;
	color: #191d21;
}

.badge.badge-white {
	background: var(--bg-white);
	color: #191d21;
}

.badge.badge-dark {
	background: #191d21;
}

h1 .badge {
	font-size: 24px;
	padding: 16px 21px;
}

h2 .badge {
	font-size: 22px;
	padding: 14px 19px;
}

h3 .badge {
	font-size: 18px;
	padding: 11px 16px;
}

h4 .badge {
	font-size: 16px;
	padding: 8px 13px;
}

h5 .badge {
	font-size: 14px;
	padding: 5px 10px;
}

h6 .badge {
	font-size: 11px;
	padding: 3px 8px;
}

.btn .badge {
	margin-left: 5px;
	padding: 4px 7px;
}

.btn .badge.badge-transparent {
	background: rgba(255, 255, 255, 0.25);
	color: var(--text-white);
}

.buttons .btn {
	margin: 0 8px 10px 0;
}

.btn:focus {
	box-shadow: none !important;
	outline: none;
	border-color: transparent !important;
}

.btn:active {
	box-shadow: none !important;
	outline: none;
}

.btn:active:focus {
	box-shadow: none !important;
	outline: none;
}

.btn.btn-icon-split i,
.dropdown-item.has-icon i {
	text-align: center;
	width: 15px;
	font-size: 15px;
	float: left;
	margin-right: 10px;
}

.btn {
	font-weight: 500;
	font-size: 14px;
	line-height: 24px;
	padding: 12px 30px;
	letter-spacing: 0.5px;
	border-radius: 50px;
}

.btn.btn-icon-split {
	position: relative;
}

.btn.btn-icon-split i {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 45px;
	border-radius: 3px 0 0 3px;
	line-height: 32px;
}

.btn.btn-icon-split div {
	margin-left: 40px;
}

.btn.btn-icon-noflo-splitat {
	display: table;
	text-align: right;
}

.btn.btn-icon-noflo-splitat i {
	float: none;
	margin: 0;
	display: table-cell;
	vertical-align: middle;
	width: 30%;
}

.btn.btn-icon-noflo-splitat div {
	display: table-cell;
	vertical-align: middle;
	width: 70%;
	text-align: left;
	padding-left: 10px;
}

.btn>i {
	margin-left: 0 !important;
}

.btn.btn-lg {
	padding: 0.55rem 1.5rem;
	font-size: 12px;
}

.btn.btn-lg.btn-icon-split i {
	line-height: 42px;
}

.btn.btn-lg.btn-icon-split div {
	margin-left: 25px;
}

.btn.btn-sm {
	padding: 0.1rem 0.4rem;
	font-size: 12px;
}

.btn.btn-icon .ion,
.btn.btn-icon .fas,
.btn.btn-icon .far,
.btn.btn-icon .fab,
.btn.btn-icon .fal {
	margin-left: 0 !important;
	font-size: 12px;
}

.btn.btn-icon.icon-left .ion,
.btn.btn-icon.icon-left .fas,
.btn.btn-icon.icon-left .far,
.btn.btn-icon.icon-left .fab,
.btn.btn-icon.icon-left .fal {
	margin-right: 3px;
}

.btn.btn-icon.icon-right .ion,
.btn.btn-icon.icon-right .fas,
.btn.btn-icon.icon-right .far,
.btn.btn-icon.icon-right .fab,
.btn.btn-icon.icon-right .fal {
	margin-left: 3px !important;
}

.btn-action {
	color: var(--text-white) !important;
	line-height: 25px;
	font-size: 12px;
	min-width: 35px;
	min-height: 35px;
}

.btn-secondary,
.btn-secondary.disabled {
	box-shadow: 0 2px 6px #e1e5e8;
	background: #cdd3d8;
	border-color: #cdd3d8;
	color: var(--text-white);
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-secondary.disabled:hover,
.btn-secondary.disabled:focus,
.btn-secondary.disabled:active {
	background: #bfc6cd !important;
	color: var(--text-white) !important;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active,
.btn-outline-secondary.disabled:hover,
.btn-outline-secondary.disabled:focus,
.btn-outline-secondary.disabled:active {
	background: #cdd3d8 !important;
	color: var(--text-white) !important;
}

.btn-success,
.btn-success.disabled {
	box-shadow: 0 2px 6px #8edc9c;
	background: #54ca68;
	border-color: #54ca68;
	color: var(--text-white);
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.disabled:hover,
.btn-success.disabled:focus,
.btn-success.disabled:active {
	background: #41c457 !important;
	color: var(--text-white) !important;
}

.btn-outline-success:hover,
.btn-outline-success:focus,
.btn-outline-success:active,
.btn-outline-success.disabled:hover,
.btn-outline-success.disabled:focus,
.btn-outline-success.disabled:active {
	background: #54ca68 !important;
	color: var(--text-white) !important;
}

.btn-danger,
.btn-danger.disabled {
	box-shadow: 0 2px 6px #fd9b96;
	background: #fc544b;
	border-color: #fc544b;
	color: var(--text-white);
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.disabled:hover,
.btn-danger.disabled:focus,
.btn-danger.disabled:active {
	background: #fb160a !important;
}

.btn-outline-danger:hover,
.btn-outline-danger:focus,
.btn-outline-danger:active,
.btn-outline-danger.disabled:hover,
.btn-outline-danger.disabled:focus,
.btn-outline-danger.disabled:active {
	background: #fb160a !important;
	color: var(--text-white) !important;
}

.btn-dark,
.btn-dark.disabled {
	box-shadow: 0 2px 6px #728394;
	background: #191d21;
	border-color: #191d21;
	color: var(--text-white);
}

.btn-dark:hover,
.btn-dark:focus,
.btn-dark:active,
.btn-dark.disabled:hover,
.btn-dark.disabled:focus,
.btn-dark.disabled:active {
	background: var(--black) !important;
}

.btn-outline-dark:hover,
.btn-outline-dark:focus,
.btn-outline-dark:active,
.btn-outline-dark.disabled:hover,
.btn-outline-dark.disabled:focus,
.btn-outline-dark.disabled:active {
	background: var(--black) !important;
	color: var(--text-white) !important;
}

.btn-light,
.btn-light.disabled {
	box-shadow: 0 2px 6px #e6ecf1;
	background: #e3eaef;
	border-color: #e3eaef;
	color: #191d21;
}

.btn-light:hover,
.btn-light:focus,
.btn-light:active,
.btn-light.disabled:hover,
.btn-light.disabled:focus,
.btn-light.disabled:active {
	background: #c3d2dc !important;
}

.btn-outline-light,
.btn-outline-light.disabled {
	border-color: #e3eaef;
	color: #e3eaef;
}

.btn-outline-light:hover,
.btn-outline-light:focus,
.btn-outline-light:active,
.btn-outline-light.disabled:hover,
.btn-outline-light.disabled:focus,
.btn-outline-light.disabled:active {
	background: #e3eaef !important;
	color: var(--text-white) !important;
}

.btn-warning,
.btn-warning.disabled {
	box-shadow: 0 2px 6px #ffc473;
	background: #ffa426;
	border-color: #ffa426;
	color: var(--text-white);
}

.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.disabled:hover,
.btn-warning.disabled:focus,
.btn-warning.disabled:active {
	background: #ff990d !important;
	color: var(--text-white) !important;
}

.btn-outline-warning:hover,
.btn-outline-warning:focus,
.btn-outline-warning:active,
.btn-outline-warning.disabled:hover,
.btn-outline-warning.disabled:focus,
.btn-outline-warning.disabled:active {
	background: #ffa426 !important;
	color: var(--text-white) !important;
}

.btn-info,
.btn-info.disabled {
	box-shadow: 0 2px 6px #82d3f8;
	background: #3abaf4;
	border-color: #3abaf4;
	color: var(--text-white);
}

.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.disabled:hover,
.btn-info.disabled:focus,
.btn-info.disabled:active {
	background: #0da8ee !important;
}

.btn-outline-info:hover,
.btn-outline-info:focus,
.btn-outline-info:active,
.btn-outline-info.disabled:hover,
.btn-outline-info.disabled:focus,
.btn-outline-info.disabled:active {
	background: #0da8ee !important;
	color: var(--text-white) !important;
}

.btn-primary,
.btn-primary.disabled {
	/* box-shadow: 0 2px 6px var(--theme-color-light); */
	background: var(--theme-color) !important;
	border-color: var(--theme-color) !important;
}

.btn-primary:focus,
.btn-primary.disabled:focus {
	background: var(--theme-color) !important;
}

.btn-primary:focus:active,
.btn-primary.disabled:focus:active {
	background: var(--theme-color) !important;
}

.btn:hover {
	border-color: transparent !important;
}

.btn.btn-secondary:hover {
	border-color: #bfc6cd;
}

.btn-primary:active,
.btn-primary:hover,
.btn-primary.disabled:active,
.btn-primary.disabled:hover {
	background: var(--theme-color) !important;
}

.btn-outline-primary,
.btn-outline-primary.disabled {
	border-color: var(--theme-color);
	color: var(--theme-color);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active,
.btn-outline-primary.disabled:hover,
.btn-outline-primary.disabled:focus,
.btn-outline-primary.disabled:active {
	background: var(--theme-color) !important;
	color: var(--text-white);
}

.btn-outline-white,
.btn-outline-white.disabled {
	border-color: var(--text-white);
	color: var(--text-white);
}

.btn-outline-white:hover,
.btn-outline-white:focus,
.btn-outline-white:active,
.btn-outline-white.disabled:hover,
.btn-outline-white.disabled:focus,
.btn-outline-white.disabled:active {
	background: var(--text-white);
	color: var(--theme-color);
}

.btn-round {
	border-radius: 30px;
	padding-left: 34px;
	padding-right: 34px;
}

.btn-social-icon,
.btn-social {
	border: none;
	border-radius: 3px;
}

.btn-social-icon {
	color: var(--text-white) !important;
	padding-left: 18px;
	padding-right: 18px;
}

.btn-social-icon> :first-child {
	font-size: 16px;
}

.btn-social {
	padding: 12px 12px 12px 50px;
	color: var(--text-white) !important;
	font-weight: 500;
}

.btn-social> :first-child {
	width: 55px;
	line-height: 50px;
	border-right: none;
}

.btn-reddit {
	color: var(--black) !important;
}

.btn-group .btn.active {
	background: var(--theme-color);
	color: var(--text-white);
}

.btn-progress {
	position: relative;
	background-image: url("../img/spinner-white.svg");
	background-position: center;
	background-repeat: no-repeat;
	background-size: 30px;
	color: transparent !important;
	pointer-events: none;
}

.media .media-right {
	float: right;
	color: var(--theme-color);
	font-weight: 600;
	font-size: 16px;
}

.media .media-icon {
	font-size: 20px;
	margin-right: 15px;
	line-height: 1;
}

.media .media-title {
	margin-top: 0;
	margin-bottom: 5px;
	font-weight: 600;
	font-size: 15px;
	color: #34395e;
}

.media .media-title a {
	font-weight: inherit;
	color: var(--black);
}

.media .media-description {
	line-height: 24px;
	color: #34395e;
}

.media .media-links {
	margin-top: 10px;
}

.media .media-links a {
	font-size: 12px;
	color: #999;
}

.media .media-progressbar {
	flex: 1;
}

.media .media-progressbar .progress-text {
	font-size: 12px;
	font-weight: 600;
	margin-bottom: 5px;
	color: #34395e;
}

.media .media-cta {
	margin-left: 40px;
}

.media .media-cta .btn {
	padding: 5px 15px;
	border-radius: 30px;
	font-size: 12px;
}

.media .media-items {
	display: flex;
}

.media .media-items .media-item {
	flex: 1;
	text-align: center;
	padding: 0 15px;
}

.media .media-items .media-item .media-label {
	font-weight: 600;
	font-size: 12px;
	color: #34395e;
	letter-spacing: 0.5px;
}

.media .media-items .media-item .media-value {
	font-weight: 700;
	font-size: 18px;
}

.breadcrumb {
	background: #f9f9f9;
}

.breadcrumb .breadcrumb-item {
	line-height: 1;
}

.breadcrumb .breadcrumb-item i {
	margin-right: 5px;
}

.accordion {
	display: inline-block;
	width: 100%;
	margin-bottom: 10px;
}

.accordion .accordion-header,
.accordion .accordion-body {
	padding: 10px 15px;
}

.accordion .accordion-header {
	background: #f9f9f9;
	border-radius: 3px;
	cursor: pointer;
	transition: all 0.5s;
}

.accordion .accordion-header h4 {
	line-height: 1;
	margin: 0;
	font-size: 14px;
	font-weight: 700;
}

.accordion .accordion-header:hover {
	background: #f2f2f2;
}

.accordion .accordion-header[aria-expanded="true"] {
	box-shadow: 0 2px 6px #acb5f6;
	background: var(--theme-color);
	color: var(--text-white);
}

.accordion .accordion-body {
	line-height: 24px;
}

.popover {
	box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
	border-color: transparent;
}

.popover .manual-arrow {
	position: absolute;
	bottom: -15px;
	font-size: 26px;
	left: 50%;
	-webkit-transform: translateX(-50%);
	transform: translateX(-50%);
	color: var(--text-white);
}

.bs-popover-auto[x-placement^="left"] .arrow::before,
.bs-popover-left .arrow::before {
	border-left-color: #f2f2f2;
}

.bs-popover-auto[x-placement^="bottom"] .arrow::before,
.bs-popover-bottom .arrow::before {
	border-bottom-color: #f2f2f2;
}

.bs-popover-auto[x-placement^="top"] .arrow::before,
.bs-popover-top .arrow::before {
	border-top-color: #f2f2f2;
}

.bs-popover-auto[x-placement^="right"] .arrow::before,
.bs-popover-right .arrow::before {
	border-right-color: #f2f2f2;
}

.popover .popover-header {
	background: transparent;
	border: none;
	padding-bottom: 0;
	padding-top: 10px;
}

.popover .popover-body {
	padding: 15px;
	line-height: 24px;
}

.sm-gutters {
	margin-left: -5px;
	margin-right: -5px;
}

.sm-gutters>.col,
.sm-gutters>[class*="col-"] {
	padding-left: 5px;
	padding-right: 5px;
}

.navbar {
	height: 70px;
	left: 270px;
	right: 0px;
	position: absolute;
	z-index: 890;
	background: var(--text-white);
	border-bottom: 2px solid #EBEBEB;
}

.navbar.active {
	background: var(--theme-color);
	box-shadow: rgba(103, 119, 239, 0.2) rgba(0, 0, 0, 0.1);
}

.navbar-bg {
	content: " ";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 70px;
	z-index: -1;
}

.navbar {
	align-items: center;
}

.navbar .navbar-brand {
	color: var(--text-white);
	text-transform: uppercase;
	letter-spacing: 3px;
	font-weight: 700;
}

.navbar .form-inline .form-control {
	background: var(--text-white);
	border-color: transparent;
	padding-left: 20px;
	padding-right: 0;
	margin-right: -6px;
	min-height: 46px;
	font-weight: 500;
	border-radius: 3px 0 0 3px;
	transition: all 1s;
}

.navbar .form-inline .form-control:focus,
.navbar .form-inline .form-control:focus+.btn {
	position: relative;
	z-index: 9001;
}

.navbar .form-inline .form-control:focus+.btn+.search-backdrop {
	opacity: 0.6;
	visibility: visible;
}

.navbar .form-inline .form-control:focus+.btn+.search-backdrop+.search-result {
	opacity: 1;
	visibility: visible;
	top: 80px;
}

.navbar .form-inline .btn {
	border-radius: 0 3px 3px 0;
	background: var(--text-white);
	padding: 9px 15px 9px 15px;
	border-color: transparent;
}

.navbar .form-inline .search-element .form-control {
	border-radius: 5px 0 0 5px;
}

.navbar .form-inline .search-element .btn {
	border-radius: 0px 5px 5px 0px;
}

.navbar .form-inline .search-backdrop {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9000;
	background: var(--black);
	opacity: 0;
	visibility: hidden;
	transition: all 0.5s;
}

.navbar .form-inline .search-result {
	position: absolute;
	z-index: 9002;
	top: 100px;
	background: var(--text-white);
	border-radius: 3px;
	width: 450px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.5s;
}

.navbar .form-inline .search-result:before {
	position: absolute;
	top: -26px;
	left: 34px;
	content: "\f0d8";
	font-weight: 600;
	font-family: "Font Awesome 5 Free";
	color: var(--text-white);
	font-size: 30px;
}

.navbar .form-inline .search-result .search-header {
	padding: 13px 18px 2px 18px;
	text-transform: uppercase;
	letter-spacing: 1.3px;
	font-weight: 600;
	font-size: 10px;
	color: #bcc1c6;
}

.navbar .form-inline .search-result .search-item {
	display: flex;
}

.navbar .form-inline .search-result .search-item a {
	display: block;
	padding: 13px 18px;
	text-decoration: none;
	color: #34395e;
	font-weight: 600;
	display: flex;
	align-items: center;
}

.navbar .form-inline .search-result .search-item a:hover {
	background: #fbfbff;
}

.navbar .form-inline .search-result .search-item a:not(.search-close) {
	width: 100%;
}

.navbar .form-inline .search-result .search-item a i {
	margin-left: 0 !important;
}

.navbar .form-inline .search-result .search-item .search-icon {
	width: 35px;
	height: 35px;
	line-height: 35px;
	text-align: center;
	border-radius: 50%;
}

.navbar .active .nav-link {
	color: var(--text-white);
	font-weight: 700;
}

.navbar .navbar-text {
	color: var(--text-white);
}

.navbar .nav-link {
	color: #f2f2f2;
	padding-left: 12px !important;
	padding-right: 12px !important;
	height: 100%;
}

.navbar .nav-link.nav-link-lg div {
	margin-top: 3px;
}

.navbar .nav-link.nav-link-lg i {
	margin-left: 0 !important;
	font-size: 18px;
	line-height: 32px;
}

.navbar .nav-link.nav-link-lg .feather {
	width: 20px;
	height: 20px;
	stroke: currentColor;
	stroke-width: 2;
	stroke-linecap: round;
	stroke-linejoin: round;
	fill: none;
	color: var(--black);
}

.navbar .nav-link.nav-link-user {
	color: var(--text-white);
	padding-top: 4px;
	padding-bottom: 4px;
	font-weight: 600;
	padding-right: 12px !important;
}

.navbar .nav-link.nav-link-user img {
	width: 30px;
}

.navbar .nav-link.nav-link-user:after {
	content: none;
}

.navbar .nav-link.nav-link-user .user-img-radious-style {
	border-radius: 6px;
	box-shadow: 4px 3px 6px 0 rgba(0, 0, 0, 0.2);
}

.navbar .nav-link.nav-link-img {
	padding-top: 4px;
	padding-bottom: 4px;
	border-radius: 50%;
	overflow: hidden;
}

.navbar .nav-link.nav-link-img .flag-icon {
	box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
	border-radius: 50%;
	line-height: 18px;
	height: 22px;
	width: 22px;
	background-size: cover;
}

.navbar .dropdown-list-toggle .message-toggle .headerBadge1 {
	position: absolute;
	top: 4px;
	right: 0px;
	font-weight: 300;
	padding: 3px 6px;
	background: #6677ef;
	border-radius: 10px;
}

.navbar .dropdown-list-toggle .notification-toggle .headerBadge2 {
	position: absolute;
	top: 5px;
	right: 0px;
	font-weight: 300;
	padding: 2px 5px;
	background: #67be7e;
	border-radius: 5px;
}

.remove-caret:after {
	display: none;
}

.navbar .nav-link:hover {
	color: var(--text-white);
}

.navbar .nav-link.disabled {
	color: var(--text-white);
	opacity: 0.6;
}

.nav-collapse {
	display: flex;
}

@media (max-width: 575.98px) {
	body.search-show .navbar .form-inline .search-element {
		display: block;
	}

	.navbar .form-inline .search-element {
		position: absolute;
		top: 10px;
		left: 10px;
		right: 10px;
		z-index: 892;
		display: none;
	}

	.navbar .form-inline .search-element .form-control {
		float: left;
		border-radius: 3px 0 0 3px;
		width: calc(100% - 43px) !important;
	}

	.navbar .form-inline .search-element .btn {
		margin-top: 1px;
		border-radius: 0 3px 3px 0;
	}

	.navbar .form-inline .search-result {
		width: 100%;
	}

	.navbar .form-inline .search-backdrop {
		display: none;
	}

	.navbar .nav-link.nav-link-lg div {
		display: none;
	}

	.navbar .nav-link {
		padding-left: 8px !important;
		padding-right: 8px !important;
	}
}

@media (min-width: 576px) and (max-width: 767.98px) {
	.navbar .form-inline .search-element {
		display: block;
	}
}

@media (min-width: 768px) and (max-width: 991.98px) {
	.collapse {
		position: relative;
	}

	.collapse .navbar-nav {
		position: absolute;
	}
}

@media (max-width: 1024px) {
	.nav-collapse {
		position: relative;
	}

	.nav-collapse .navbar-nav {
		box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
		position: absolute;
		top: 40px;
		left: 0;
		width: 200px;
		display: none;
	}

	.nav-collapse .navbar-nav.show {
		display: block;
	}

	.nav-collapse .navbar-nav .nav-item:first-child {
		border-radius: 3px 3px 0 0;
	}

	.nav-collapse .navbar-nav .nav-item:last-child {
		border-radius: 0 0 3px 3px;
	}

	.nav-collapse .navbar-nav .nav-item .nav-link {
		background: var(--text-white);
		color: #6c757d;
	}

	.nav-collapse .navbar-nav .nav-item .nav-link:hover {
		background: #fcfcfd;
		color: var(--theme-color);
	}

	.nav-collapse .navbar-nav .nav-item:focus>a,
	.nav-collapse .navbar-nav .nav-item.active>a {
		background: var(--theme-color);
		color: var(--text-white);
	}

	.navbar {
		left: 5px;
		right: 0;
	}

	.navbar .dropdown-menu {
		position: absolute;
	}

	.navbar .navbar-nav {
		flex-direction: row;
	}

	.navbar-expand-lg .navbar-nav .dropdown-menu-right {
		right: 0;
		left: auto;
	}
}

.app-dropdown {
	width: 280px !important;
}

.app-icon-dropdown {
	padding-left: 15px;
}

.app-icon-dropdown li {
	display: inline-block;
	margin-top: 10px;
	margin-bottom: 10px;
}

.app-icon-dropdown li:hover {
	background: rgba(181, 174, 174, 0.22);
}

.app-icon-dropdown li a {
	display: inline-block;
	padding-bottom: 10px;
	padding-top: 10px;
	text-align: center;
	color: #878787;
	text-transform: capitalize;
	width: 80px;
}

.app-icon-dropdown li a:hover {
	text-decoration: none;
}

.app-icon-dropdown .email-icon .material-icons {
	color: #71aa68;
	width: 100%;
}

.app-icon-dropdown .blog-icon .material-icons {
	color: #0080c0;
	width: 100%;
}

.app-icon-dropdown .cal-icon .material-icons {
	color: #ff8000;
	width: 100%;
}

.app-icon-dropdown .chat-icon .material-icons {
	color: #ff0080;
	width: 100%;
}

.app-icon-dropdown .gallery-icon .material-icons {
	color: #8000ff;
	width: 100%;
}

.app-icon-dropdown .profile-icon .material-icons {
	color: #008040;
	width: 100%;
}

@media (max-width: 619px) {
	.navbar .form-inline .search-element {
		display: none;
	}
}

.dropdown-item.has-icon i {
	margin-top: -1px;
	font-size: 13px;
}

.dropdown-menu {
	box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
	border: none;
	width: 200px;
}

.dropdown-menu.show {
	display: block !important;
}

.dropdown-menu a {
	font-size: 16px;
}

.dropdown-menu .dropdown-title {
	text-transform: uppercase;
	font-size: 10px;
	letter-spacing: 1.5px;
	font-weight: 700;
	color: #191d21 !important;
	padding: 10px 20px;
	line-height: 20px;
	color: #98a6ad;
}

.dropdown-menu.dropdown-menu-sm a {
	font-size: 14px;
	letter-spacing: normal;
	padding: 10px 20px;
	color: #6c757d;
}

a.dropdown-item {
    padding: 12px 30px;
    font-weight: 500;
    display: flex;
    align-items: center;
    letter-spacing: 1px;
    line-height: 1;
}
a.dropdown-item svg {
    margin-right: 10px;
    width: 100%;
    max-width: 17px;
}
a.dropdown-item:focus,
a.dropdown-item:active,
a.dropdown-item.active {
	background: #e9e9e9;
	color: #666666 !important;
	border-radius: 30px;
}

.dropdown-divider {
	border-top-color: #f9f9f9;
}

.dropdown-list {
	width: 300px;
	padding: 0;
}

.dropdown-list .dropdown-item {
	display: inline-block;
	width: 100%;
	padding-top: 15px;
	padding-bottom: 15px;
	font-size: 13px;
	border-bottom: 1px solid #f9f9f9;
}

.dropdown-list .dropdown-item.dropdown-item-header:hover {
	background: transparent;
}

.dropdown-list .dropdown-item .time {
	margin-top: 10px;
	font-weight: 600;
	text-transform: uppercase;
	font-size: 10px;
	letter-spacing: 0.5px;
}

.dropdown-list .dropdown-item .dropdown-item-avatar {
	float: left;
	width: 50px;
	text-align: right;
	position: relative;
}

.dropdown-list .dropdown-item .dropdown-item-avatar img {
	width: 100%;
}

.dropdown-list .dropdown-item .dropdown-item-avatar .is-online {
	position: absolute;
	bottom: 0;
	right: 0;
}

.dropdown-list .dropdown-item .dropdown-item-desc {
	line-height: 24px;
	white-space: normal;
	color: #34395e;
	margin-left: 60px;
}

.dropdown-list .dropdown-item .dropdown-item-desc b {
	font-weight: 600;
	color: #666;
}

.dropdown-list .dropdown-item .dropdown-item-desc p {
	margin-bottom: 0;
}

.dropdown-list .dropdown-item .dropdown-msg-item-desc {
	margin-left: 13px;
	display: inline-grid;
}

.dropdown-list .dropdown-item:focus {
	background: #e9e9e9;
}

.dropdown-list .dropdown-item:focus .dropdown-item-desc {
	color: #666 !important;
}

.dropdown-list .dropdown-item:focus .dropdown-item-desc b {
	color: #666 !important;
}

.dropdown-list .dropdown-item.dropdown-item-unread:active .dropdown-item-desc {
	color: #6c757d;
}

.dropdown-list .dropdown-item.dropdown-item-unread:active .dropdown-item-desc b {
	color: #6c757d;
}

.dropdown-list .dropdown-item:active .dropdown-item-desc {
	color: var(--text-white);
}

.dropdown-list .dropdown-item:active .dropdown-item-desc b {
	color: var(--text-white);
}

.dropdown-list .dropdown-item.dropdown-item-unread {
	background: #fbfbfb;
	border-bottom-color: #f2f2f2;
}

.dropdown-list .dropdown-item.dropdown-item-unread:focus .dropdown-item-desc {
	color: #6c757d !important;
}

.dropdown-list .dropdown-item.dropdown-item-unread:focus .dropdown-item-desc b {
	color: #6c757d !important;
}

.dropdown-list .dropdown-footer,
.dropdown-list .dropdown-header {
	letter-spacing: 0.5px;
	font-weight: 600;
	padding: 10px 15px 10px 15px;
}

.dropdown-list .dropdown-footer a,
.dropdown-list .dropdown-header a {
	font-weight: 600;
}

.dropdown-list .dropdown-list-content {
	height: 250px;
	overflow: hidden;
}

.dropdown-list .dropdown-list-content:not(.is-end):after {
	content: " ";
	position: absolute;
	bottom: 46px;
	left: 0;
	width: 100%;
	height: 60px;
}

.dropdown-list .dropdown-list-icons .dropdown-item {
	display: flex;
}

.dropdown-list .dropdown-list-icons .dropdown-item .dropdown-item-icon {
	flex-shrink: 0;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	line-height: 42px;
	text-align: center;
}

.dropdown-list .dropdown-list-icons .dropdown-item .dropdown-item-icon i {
	margin: 0;
}

.dropdown-list .dropdown-list-icons .dropdown-item .dropdown-item-desc {
	margin-left: 15px;
	line-height: 20px;
}

.dropdown-list .dropdown-list-icons .dropdown-item .dropdown-item-desc .time {
	margin: 0;
	font-size: 10px;
	color: #aaa;
	float: left;
	width: 100%;
	line-height: 20px;
}

.dropdown-list .dropdown-list-message .dropdown-item {
	display: flex;
	padding-top: 4px;
	border-bottom: 1px solid #eee;
	padding-bottom: 0px;
}

.dropdown-list .dropdown-list-message .dropdown-item .dropdown-item-icon {
	flex-shrink: 0;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	line-height: 42px;
	text-align: center;
}

.dropdown-list .dropdown-list-message .dropdown-item .dropdown-item-icon i {
	margin: 0;
}

.dropdown-list .dropdown-list-message .dropdown-item .dropdown-item-desc {
	margin-left: 15px;
	line-height: 20px;
	width: 100%;
}

.dropdown-list .dropdown-list-message .dropdown-item .dropdown-item-desc .time {
	margin: 0;
	font-size: 10px;
	color: #aaa;
	float: left;
	width: 100%;
	line-height: 20px;
}

.dropdown-list .dropdown-list-message .dropdown-item .dropdown-item-desc .messege-text {
	text-transform: none;
	font-size: 12px;
	color: #6d6c6c;
}

.dropdown-list .dropdown-list-message .dropdown-item .dropdown-item-desc .message-user {
	font-size: 14px;
	font-weight: 600;
	color: #39393c;
}

.dropdown-flag .dropdown-item {
	font-weight: 600;
}

.dropdown-flag .dropdown-item .flag-icon {
	width: 20px;
	height: 13px;
	margin-right: 7px;
	margin-top: -6px;
}

.dropdown-flag .dropdown-item.active {
	background: var(--theme-color);
	color: var(--text-white);
}

@media (max-width: 479px) {
	.dropdown-list-toggle:first-child .dropdown-list {
		right: -100px;
	}

	.dropdown-list-toggle:nth-child(2) .dropdown-list {
		right: -60px;
	}
}

.tab-content.no-padding>.tab-pane {
	padding: 0;
}

.tab-content>.tab-pane {
	line-height: 28px;
}

.progress {
	-webkit-box-shadow: 0 0.4rem 0.6rem rgba(0, 0, 0, 0.15);
	box-shadow: 0 0.4rem 0.6rem rgba(0, 0, 0, 0.15);
}

.progress.progress-xs {
	height: 5px;
}

.progress.progress-s {
	height: 7px;
}

.progress-bar {
	background: var(--theme-color);
}

.jumbotron {
	background: #e3eaef;
}

.carousel .carousel-caption p {
	font-size: 13px;
	line-height: 24px;
}

.ionicons {
	padding: 0;
	margin: 0;
	display: flex;
	flex-wrap: wrap;
}

.ionicons li {
	width: calc(100% / 8);
	font-size: 40px;
	padding: 40px 20px;
	list-style: none;
	text-align: center;
	border-radius: 3px;
	position: relative;
	cursor: pointer;
}

.ionicons li:hover {
	opacity: 0.8;
}

.ionicons li .icon-name {
	position: absolute;
	top: 100%;
	left: 50%;
	width: 100%;
	-webkit-transform: translate(-50%, -100%);
	transform: translate(-50%, -100%);
	font-family: "Segoe UI";
	font-size: 12px;
	margin-top: 10px;
	line-height: 22px;
	background: #f9f9f9;
	border-radius: 3px;
	padding: 10px;
	display: none;
}

.icon-preview {
	width: 30px;
}

.icon-preview i {
	font-size: 20px;
}

.icon-container .preview {
	display: flex;
	padding: 12px;
	border-radius: 5px;
	margin: 5px;
	border: 1px solid #c1bbbb;
}

.icon-container .preview .icon-name {
	padding-left: 10px;
}

.icon-feather-container div {
	padding: 12px;
	border: 1px solid #c1bbbb;
}

a.bb {
	text-decoration: none;
	border-bottom: 1px solid var(--theme-color);
	padding-bottom: 1px;
}

.form-divider {
	display: inline-block;
	width: 100%;
	margin: 10px 0;
	font-size: 16px;
	font-weight: 600;
}

.ui-sortable-handle,
.sort-handler {
	cursor: move;
}

.text-job {
	font-size: 10px;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 700;
	color: #34395e;
}

.text-time {
	font-size: 12px;
	color: #666;
	font-weight: 500;
	margin-bottom: 10px;
}

.bullet,
.slash {
	display: inline;
	margin: 0 4px;
}

.bullet:after {
	content: "\2022";
}

.slash:after {
	content: "/";
}

.login-brand {
	margin: 20px 0;
	margin-bottom: 40px;
	font-size: 24px;
	text-transform: uppercase;
	letter-spacing: 4px;
	color: #666;
	text-align: center;
}

.font-weight-600 {
	font-weight: 600 !important;
}

.budget-price {
	display: inline-block;
	width: 100%;
	display: flex;
	align-items: center;
	margin-bottom: 3px;
}

.budget-price .budget-price-square {
	width: 15px;
	height: 3px;
	background: #f9f9f9;
}

.budget-price .budget-price-label {
	font-size: 12px;
	font-weight: 600;
	margin-left: 5px;
}

.gradient-bottom {
	position: relative;
}

.gradient-bottom:after {
	content: " ";
	position: absolute;
	bottom: 41px;
	left: 0;
	width: 100%;
	background-image: linear-gradient(to bottom,
			rgba(255, 255, 255, 0),
			rgba(255, 255, 255, 0.4),
			rgba(255, 255, 255, 0.8));
	height: 60px;
}

.text-small {
	font-size: 12px;
	line-height: 20px;
}

.text-title {
	font-size: 14px;
	color: #34395e;
	font-weight: 600;
}

.img-shadow {
	box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
}

.colors {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -5px;
}

.colors .color {
	border-radius: 3px;
	width: calc((100% / 4) - 10px);
	padding: 10px;
	height: 60px;
	line-height: 40px;
	text-align: center;
	margin: 5px;
}

blockquote {
	padding: 20px;
	padding-left: 40px;
	font-style: oblique;
	background: #f9f9f9;
	border-radius: 3px;
	position: relative;
	font-family: "Time new Romans";
	font-size: 16px;
	letter-spacing: 0.3px;
}

/* blockquote:before {
  content: '"';
  font-size: 30px;
  position: absolute;
  top: 10px;
  left: 20px;
  opacity: 0.2;
  } */

blockquote .blockquote-footer {
	margin-top: 10px;
}

.bg-whitesmoke {
	background: #f7f9f9 !important;
}

.ion {
	font-size: 15px;
}

.fas,
.far,
.fab,
.fal {
	font-size: 13px;
}

#visitorMap {
	height: 400px;
}

#visitorMap2,
#visitorMap3 {
	height: 350px;
}

#visitorMap4 {
	height: 190px;
}

.sidebar-gone-show {
	display: none !important;
}

pre {
	border-radius: 3px;
}

.circle-step {
	display: flex;
	margin-bottom: 10px;
}

.circle-step .circle-content {
	margin-top: 3px;
	margin-left: 13px;
}

.circle-step .circle {
	border-width: 2px;
	border-style: solid;
	border-radius: 50%;
	display: inline-block;
	width: 32px;
	height: 32px;
	line-height: 30px;
	font-size: 11px;
	text-align: center;
}

.circle-step .circle.circle-primary {
	border-color: var(--theme-color);
	color: var(--theme-color);
}

.pe-none {
	pointer-events: none;
}

.contact-map {
	width: 100%;
	height: 100%;
	min-height: 400px;
}

.shadow-primary {
	/* box-shadow: 0 2px 6px var(--theme-color-light); */
}

.shadow-secondary {
	box-shadow: 0 2px 6px #e1e5e8;
}

.shadow-success {
	box-shadow: 0 2px 6px #8edc9c;
}

.shadow-warning {
	box-shadow: 0 2px 6px #ffc473;
}

.shadow-danger {
	box-shadow: 0 2px 6px #fd9b96;
}

.shadow-info {
	box-shadow: 0 2px 6px #82d3f8;
}

.shadow-light {
	box-shadow: 0 2px 6px #e6ecf1;
}

.shadow-dark {
	box-shadow: 0 2px 6px #728394;
}

.is-online {
	width: 10px;
	height: 10px;
	background: #54ca68;
	border-radius: 50%;
	display: inline-block;
}

.gutters-xs {
	margin-right: -0.25rem;
	margin-left: -0.25rem;
}

.gutters-xs>.col,
.gutters-xs>[class*="col-"] {
	padding-right: 0.25rem;
	padding-left: 0.25rem;
}

.beep {
	position: relative;
}

.beep:after {
	content: "";
	position: absolute;
	top: 2px;
	right: 8px;
	width: 7px;
	height: 7px;
	background: #ffa426;
	border-radius: 50%;
	animation: pulsate 1s ease-out;
	animation-iteration-count: infinite;
	opacity: 1;
}

.beep.beep-sidebar:after {
	position: static;
	margin-left: 10px;
}

@media (max-width: 575.98px) {
	.fc-overflow {
		width: 100%;
		overflow: auto;
	}

	.fc-overflow #myEvent {
		width: 800px;
	}

	.ionicons li {
		width: calc(100% / 4);
	}

	.icon-wrap {
		width: 100%;
	}
}

.section {
	position: relative;
	z-index: 1;
}

.section .section-header {
	padding: 20px;
	display: flex;
	align-items: center;
}

.section .section-header h1 {
	margin-bottom: 0;
	font-weight: 700;
	display: inline-block;
	font-size: 24px;
	margin-top: 3px;
	color: #34395e;
}

.section .section-header .section-header-back {
	margin-right: 15px;
}

.section .section-header .section-header-back .btn:hover {
	background: var(--theme-color);
	color: var(--text-white);
}

.section .section-header .section-header-button {
	margin-left: 20px;
}

.section .section-header .section-header-breadcrumb {
	margin-left: auto;
	display: flex;
	align-items: center;
	background: #e7e8e9;
	padding: 15px;
	border-radius: 30px;
}

.section .section-header .section-header-breadcrumb .breadcrumb-item {
	font-size: 13px;
}

.section .section-header .btn {
	font-size: 12px;
}

.section .section-title {
	font-size: 18px;
	color: #191d21;
	font-weight: 600;
	position: relative;
	margin: 30px 0 25px 0;
}

.section .section-title+.section-lead {
	margin-top: -20px;
}

.main-wrapper-1 .section .section-header {
	margin-left: -30px;
	margin-right: -30px;
	margin-top: -10px;
	border-radius: 0;
	border-top: 1px solid #f9f9f9;
	padding-left: 35px;
	padding-right: 35px;
}

@media (max-width: 575.98px) {
	.section .section-title {
		font-size: 14px;
	}

	.section .section-header {
		flex-wrap: wrap;
		margin-bottom: 20px !important;
	}

	.section .section-header h1 {
		font-size: 18px;
	}

	.section .section-header .float-right {
		display: inline-block;
		width: 100%;
		margin-top: 15px;
	}

	.section .section-header .section-header-breadcrumb {
		flex-basis: 100%;
		margin-top: 10px;
	}
}

@media (max-width: 575.98px) {
	.page-error .page-search {
		width: 100%;
	}
}

.main-sidebar {
	position: fixed;
	top: 0;
	height: 100%;
	width: 270px;
	background: var(--text-white);
	z-index: 880;
	left: 0;
	border-right: 2px solid #EBEBEB;
}

.main-sidebar,
.navbar,
.main-content,
.main-footer {
	transition: all 0.5s;
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	-ms-transition: all 0.5s;
	-o-transition: all 0.5s;
}

body.sidebar-gone .main-sidebar {
	left: -270px;
}

.sidebar-mini .hide-sidebar-mini {
	display: none !important;
}

.sidebar-mini .main-sidebar {
	width: 65px;
	overflow: initial !important;
	position: absolute;
	box-shadow: none;
}

.sidebar-mini .main-sidebar:after {
	box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
	content: " ";
	position: fixed;
	background: var(--text-white);
	width: 65px;
	height: 100%;
	left: 0;
	top: 0;
	z-index: -1;
	opacity: 0;
	animation-name: mini-sidebar;
	animation-duration: 1.5s;
	animation-fill-mode: forwards;
}

@keyframes mini-sidebar {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

.sidebar-mini .main-sidebar .logo-name {
	display: none;
}

.sidebar-mini .main-sidebar .sidebar-user {
	margin: 0;
	height: 70px;
	padding: 15px;
}

.sidebar-mini .main-sidebar .sidebar-user .sidebar-user-picture img {
	width: 35px;
}

.sidebar-mini .main-sidebar .sidebar-user .sidebar-user-details .user-name,
.sidebar-mini .main-sidebar .sidebar-user .sidebar-user-details .user-role {
	display: none;
}

.sidebar-mini .main-sidebar .sidebar-brand-sm {
	display: block;
}

.sidebar-mini .main-sidebar .sidebar-menu>li {
	padding: 10px;
}

.sidebar-mini .main-sidebar .sidebar-menu>li.menu-header {
	padding: 0;
	font-size: 0;
	height: 2px;
}

.sidebar-mini .main-sidebar .sidebar-menu>li>a {
	border-radius: 3px;
	height: 45px;
	padding: 0;
	justify-content: center;
}

.sidebar-mini .main-sidebar .sidebar-menu>li>a .ion,
.sidebar-mini .main-sidebar .sidebar-menu>li>a .fas,
.sidebar-mini .main-sidebar .sidebar-menu>li>a .far,
.sidebar-mini .main-sidebar .sidebar-menu>li>a .fab,
.sidebar-mini .main-sidebar .sidebar-menu>li>a .fal {
	margin: 0;
	font-size: 20px;
}

.sidebar-mini .main-sidebar .sidebar-menu>li>a span {
	display: none;
}

.sidebar-mini .main-sidebar .sidebar-menu>li>a .badge {
	padding: 5px;
	position: absolute;
	top: 4px;
	right: 4px;
	font-size: 10px;
}

.sidebar-mini .main-sidebar .sidebar-menu>li>a.has-dropdown:after {
	content: initial;
}

.sidebar-mini .main-sidebar .sidebar-menu>li.active>a {
	box-shadow: 0 4px 8px #acb5f6;
	background: var(--theme-color);
	color: var(--text-white);
}

.sidebar-mini .main-sidebar .sidebar-menu>li ul.dropdown-menu {
	position: absolute;
	background: var(--text-white);
	left: 65px;
	top: 10px;
	width: 200px;
	display: none;
	box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

.sidebar-mini .main-sidebar .sidebar-menu>li ul.dropdown-menu li>a:focus,
.sidebar-mini .main-sidebar .sidebar-menu>li ul.dropdown-menu li.active>a,
.sidebar-mini .main-sidebar .sidebar-menu>li ul.dropdown-menu li.active>a:hover {
	color: var(--theme-color);
}

.sidebar-mini .main-sidebar .sidebar-menu>li ul.dropdown-menu li a {
	height: 40px;
	background: var(--text-white);
}

.sidebar-mini .main-sidebar .sidebar-menu>li ul.dropdown-menu li a.has-dropdown:after {
	content: "";
	font-family: "Font Awesome 5 Free";
	font-weight: 900;
	position: absolute;
	top: 50%;
	right: 20px;
	-webkit-transform: translate(0, -50%);
	transform: translate(0, -50%);
	font-size: 14px;
}

.sidebar-mini .main-sidebar .sidebar-menu li:hover>ul.dropdown-menu {
	display: block !important;
}

.sidebar-mini .main-sidebar .sidebar-menu li:hover>ul.dropdown-menu li:hover>a {
	background: #fcfcfd;
}

.sidebar-mini .main-sidebar .sidebar-menu li:hover>ul.dropdown-menu li .dropdown-menu {
	left: 200px;
	padding: 0;
}

.sidebar-mini .main-sidebar .sidebar-menu .menu-toggle:before {
	content: "";
}

.sidebar-mini .main-sidebar .sidebar-menu .menu-toggle:after {
	content: "";
}

.sidebar-mini .navbar {
	left: 65px;
}

.sidebar-mini .main-content,
.sidebar-mini .main-footer {
	padding-left: 90px;
}

.sidebar-mini .main-sidebar .sidebar-menu li a .feather {
	margin-right: 0px;
}

.sideBarli.activeLi svg.room_svg {
	stroke-width: 0;
}
.sideBarli svg.room_svg path {
	fill: var(--black);
}

.loader {
	position: fixed;
	left: 0px;
	top: 0px;
	width: 100%;
	height: 100%;
	z-index: 9999;
	/* background: url("../img/loading.gif") 50% 50% no-repeat #f9f9f9; */
	opacity: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f9f9f9fa;
	/* backdrop-filter: blur(40px); */
}

.spinner-border {
	border: 0.25em solid var(--theme-color);
	border-right-color: transparent;
}

.lds-ring {
	display: inline-block;
	position: relative;
	width: 80px;
	height: 80px;
}

.lds-ring div {
	box-sizing: border-box;
	display: block;
	position: absolute;
	width: 44px;
	height: 44px;
	margin: 8px;
	border: 4px solid var(--theme-color);
	border-radius: 50%;
	animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
	border-color: var(--theme-color) transparent transparent transparent;
	-webkit-animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.lds-ring div:nth-child(1) {
	animation-delay: -0.45s;
}

.lds-ring div:nth-child(2) {
	animation-delay: -0.3s;
}

.lds-ring div:nth-child(3) {
	animation-delay: -0.15s;
}

@keyframes lds-ring {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}


.main-sidebar .sidebar-brand {
	display: inline-block;
	width: 100%;
	text-align: center;
	height: 70px;
	line-height: 70px;
	border-bottom: 2px solid #EBEBEB;
	margin-bottom: 15px;
	background: #fbfbfb;
}

.main-sidebar .sidebar-brand.sidebar-brand-sm {
	display: none;
}

.main-sidebar .sidebar-brand a {
	text-decoration: none;
	font-weight: 700;
	vertical-align: bottom;
	color: var(--black);
	font-size: 30px;
}

.main-sidebar .sidebar-brand a .header-logo {
	height: 30px;
}

.main-sidebar .sidebar-user {
	display: inline-block;
	width: 100%;
	padding: 10px;
	text-align: center;
}

.main-sidebar .sidebar-user .sidebar-user-picture {
	margin-right: 10px;
}

.main-sidebar .sidebar-user .sidebar-user-picture img {
	width: 75px;
	border-radius: 50%;
}

.main-sidebar .sidebar-user .sidebar-user-details .user-name {
	white-space: nowrap;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	overflow: hidden;
	margin-top: 7px;
	margin-bottom: 3px;
	font-weight: 600;
	color: #505c66;
}

.main-sidebar .sidebar-user .sidebar-user-details .user-role {
	font-weight: 400;
	color: #868e96;
	font-size: 10px;
	letter-spacing: 0.5px;
}

.main-sidebar .sidebar-menu {
	padding: 0 10px;
	margin: 0;
}

.main-sidebar .sidebar-menu li {
	display: block;
}

.main-sidebar .sidebar-menu li.menu-header {
	padding: 3px 15px;
	color: #868e96;
	font-size: 10px;
	text-transform: uppercase;
	letter-spacing: 1.3px;
	font-weight: 600;
}

.main-sidebar .sidebar-menu li.menu-header:not(:first-child) {
	margin-top: 10px;
}

.main-sidebar .sidebar-menu li .menu-toggle:before {
	content: "\f067";
	font-family: "Font Awesome\ 5 Free";
	position: absolute;
	font-size: 10px;
	right: 17px;
	transform: scale(1);
	transition: all 0.3s;
}

.main-sidebar .sidebar-menu li .menu-toggle:after {
	content: "\f068";
	font-family: "Font Awesome\ 5 Free";
	position: absolute;
	font-size: 10px;
	right: 17px;
	transform: scale(0);
	transition: all 0.3s;
}

.main-sidebar .sidebar-menu li .menu-toggle.toggled:before {
	transform: scale(0);
}

.main-sidebar .sidebar-menu li .menu-toggle.toggled:after {
	transform: scale(1);
}

.main-sidebar .sidebar-menu li a {
	position: relative;
	display: flex;
	align-items: center;
	height: 50px;
	padding: 0 10px;
	width: 100%;
	letter-spacing: 0.3px;
	color: var(--black);
	font-weight: 500;
	text-decoration: none;
}

.main-sidebar .sidebar-menu li a .badge {
	float: right;
	padding: 5px 10px;
	margin-top: 2px;
}

.main-sidebar .sidebar-menu li a i {
	width: 28px;
	font-size: 15px;
	margin-right: 10px;
	text-align: center;
}

.main-sidebar .sidebar-menu li a .feather {
	height: 25px;
	width: 25px;
	margin-right: 10px;
	text-align: center;
}

.sideBarli.activeLi svg {
	stroke: var(--theme-active-text-color);
}

.main-sidebar .sidebar-menu li a span {
	width: 100%;
	line-height: 1px;
	font-size: 16px;
	text-transform: capitalize;
}

.main-sidebar .sidebar-menu li a:hover {
	background: #f2f5f8;
}


.main-sidebar .sidebar-menu li.activeLi a {
	font-weight: 500;
	background: var(--theme-color);
	color: var(--text-white);
}

.main-sidebar .sidebar-menu li.activeLi ul.dropdown-menu {
	background: #fcfcfd;
}

.main-sidebar .sidebar-menu li.activeLi>ul.dropdown-menu {
	display: block;
}

.main-sidebar .sidebar-menu li.activeLi>ul.dropdown-menu li a:hover {
	background: #fcfcfd;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu {
	padding: 0;
	margin: 0;
	display: none;
	position: static;
	float: none;
	width: 100%;
	box-shadow: none;
	background: transparent;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li a {
	color: #60686f;
	height: 35px;
	padding-left: 50px;
	font-weight: 400;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li a:hover {
	color: var(--theme-color);
	background: inherit;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li a:hover:before {
	color: var(--theme-color);
	font-weight: 600;
	left: 35px;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li a:before {
	content: "\f105";
	font-family: "Font Awesome 5 Free";
	font-weight: 900;
	font-size: 12px;
	position: absolute;
	transition: 0.5s;
	left: 30px;
	color: #868e96;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li.active>a {
	color: var(--theme-color);
	font-weight: 600;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li.active>a:before {
	color: var(--theme-color);
	font-weight: 600;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li a i {
	margin-top: 1px;
	text-align: center;
}

.main-sidebar .sidebar-menu li ul.dropdown-menu li ul.dropdown-menu {
	padding-left: 10px;
}

.main-content {
	padding-left: 295px;
	padding-right: 30px;
	padding-top: 100px;
	width: 100%;
	position: relative;
}

.main-footer {
	padding: 20px 30px 20px 280px;
	margin-top: 40px;
	color: #98a6ad;
	border-top: 1px solid #e3eaef;
	display: inline-block;
	background: var(--text-white);
	font-weight: 600;
	text-transform: uppercase;
	font-size: 11px;
	width: 100%;
}

.main-footer .footer-left {
	float: left;
}

.main-footer .footer-right {
	float: right;
}

.simple-footer {
	text-align: center;
	margin-top: 40px;
	margin-bottom: 40px;
}

body:not(.sidebar-mini) .sidebar-style-1 .sidebar-menu li.active a {
	background: var(--theme-color);
	color: var(--text-white);
}

body:not(.sidebar-mini) .sidebar-style-1 .sidebar-menu li.active ul.dropdown-menu li a {
	color: #e8ebfd;
}

body:not(.sidebar-mini) .sidebar-style-1 .sidebar-menu li.active ul.dropdown-menu li a:hover {
	background: var(--theme-color);
	color: var(--text-white);
}

body:not(.sidebar-mini) .sidebar-style-1 .sidebar-menu li.active ul.dropdown-menu li.active a {
	color: var(--text-white);
}

body:not(.sidebar-mini) .sidebar-style-2 .sidebar-menu>li.active>a {
	padding-left: 20px;
	position: relative;
	color: var(--theme-color);
}

body:not(.sidebar-mini) .sidebar-style-2 .sidebar-menu li.active ul.dropdown-menu li a {
	background: var(--bg-white);
}

.theme-setting {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 2002;
}

.theme-setting .theme-setting-toggle {
	transition: all 0.5s;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background: #f73f52;
	color: var(--text-white);
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	text-align: center;
	line-height: 60px;
	cursor: pointer;
}

.theme-setting .theme-setting-toggle i {
	font-size: 24px;
}

.theme-setting .theme-setting-options {
	transition: all 0.5s;
	transition-delay: 0.3s;
	z-index: -1;
	position: absolute;
	left: -220px;
	bottom: 0;
	height: 150px;
	width: 50px;
	background: #e8e6e6;
	box-shadow: 0 0 40px rgba(0, 0, 0, 0.05);
	border-radius: 12px;
	visibility: hidden;
	opacity: 0;
}

.theme-setting .theme-setting-options ul {
	padding: 0;
	margin: 0;
	width: 100%;
	display: inline-block;
	margin-left: 20px;
}

.theme-setting .theme-setting-options ul li {
	width: 20px;
	height: 20px;
	background: var(--black);
	margin-right: 10px;
	margin-top: 15px;
	border-radius: 3px;
	display: inline-block;
	cursor: pointer;
	opacity: 0;
	transition: all 0.5s;
}

.theme-setting .theme-setting-options ul li:hover {
	opacity: 0.8;
}

.theme-setting.active .theme-setting-toggle {
	margin: 5px;
	box-shadow: none;
	line-height: 50px;
	width: 40px;
	height: 40px;
	transform: rotate(90deg);
}

.theme-setting.active .theme-setting-options {
	visibility: visible;
	opacity: 1;
	width: 220px;
}

.theme-setting.active .theme-setting-options ul li {
	opacity: 1;
	transition-delay: 0.3s;
}

@media (max-width: 1024px) {
	.sidebar-gone-hide {
		display: none !important;
	}

	.sidebar-gone-show {
		display: block !important;
	}

	.main-sidebar {
		position: fixed !important;
		margin-top: 0 !important;
		z-index: 891;
	}

	body.layout-2 .main-wrapper,
	body.layout-3 .main-wrapper {
		width: 100%;
		padding: 0;
		display: block;
	}

	.main-content {
		padding-left: 30px;
		padding-right: 30px;
		width: 100% !important;
	}

	.main-footer {
		padding-left: 30px;
	}

	body.search-show {
		overflow: hidden;
	}

	body.search-show .navbar {
		z-index: 892;
	}

	body.sidebar-show {
		overflow: hidden;
	}
}

.activeLi {
	background: var(--theme-color);
	border-radius: 10px;
	padding: 0px 0px !important;
	color: white !important;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-ms-border-radius: 10px;
	-o-border-radius: 10px;
	/* filter: drop-shadow(0px 4px 11px var(--theme-color-light));
  -webkit-filter: drop-shadow(0px 4px 11px var(--theme-color-light)); */
}


.activeLi a i {
	color: var(--text-white) !important;
}

.activeLi a span {
	color: var(--text-white) !important;
}


.sidebar-menu li {
	margin: 10px 0px !important;
	border-radius: 25px !important;
	-webkit-border-radius: 25px !important;
	-moz-border-radius: 25px !important;
	-ms-border-radius: 25px !important;
	-o-border-radius: 25px !important;
	overflow: hidden;
}


.sidebar-menu li a:hover {
	color: var(--text-white) !important;
	background: var(--theme-color) !important;
	/* filter: drop-shadow(0px 4px 11px var(--theme-color-light)) !important;
  -webkit-filter: drop-shadow(0px 4px 11px var(--theme-color-light)) !important; */
}


.sidebar-menu li a:hover i {
	color: var(--text-white) !important;
}

.sidebar-menu li a:hover span {
	color: var(--text-white) !important;
}

table.dataTable th,
table.dataTable td {
	vertical-align: middle;
	white-space: nowrap !important;
}

.dataTables_filter {
	text-align: right;
}

.dataTables_filter label {
	display: inline-flex;
	align-items: center;
	font-size: 18px;
}

.dataTables_filter label input {
	margin-left: 10px;
}

.dataTables_length label {
	display: inline-flex;
	align-items: center;
	justify-content: flex-start;
	font-size: 18px;
	margin-bottom: 20px;
}

.dataTables_length label select {
	margin: 0 10px;
}

.pagination {
	justify-content: flex-end;
	margin: 0;
}

.dataTables_empty {
	background: white;
	opacity: 1;
	text-align: center;
	font-size: 18px;
	padding: 20px 0 !important;
	font-weight: 500;
}

.dataTables_wrapper {
	font-size: 16px !important;
}

.dataTables_wrapper .row:last-child {
	align-items: center;
}

.pagination .paginate_button .page-link {
	margin: 0 5px;
	box-shadow: 0px 0px 8px 0px #d5d5d5;
}

.switch {
	position: relative;
	display: inline-block;
	width: 45px;
	height: 28px;
}

.switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #ccc;
	-webkit-transition: .4s;
	transition: .4s;
	border-radius: 50px;
}

.slider:before {
	position: absolute;
	content: "";
	height: 20px;
	width: 20px;
	left: 4px;
	bottom: 4px;
	background: white;
	-webkit-transition: .4s;
	transition: .4s;
	border-radius: 50px;
}

input:checked+.slider {
	background: #2196F3;
}

input:focus+.slider {
	box-shadow: 0 0 1px #2515b6;
}

input:checked+.slider:before {
	-webkit-transform: translateX(16px);
	-ms-transform: translateX(16px);
	transform: translateX(16px);
}

/* Rounded sliders */
.slider.round {
	border-radius: 34px;
}

.slider.round:before {
	border-radius: 50%;
}


.carousel-img {
	width: 300px;
	height: 100%;
	max-height: 410px;
	margin: 0 auto;
}

.image-size {
	width: inherit;
	height: inherit;
	max-width: -webkit-fill-available;
	max-height: -webkit-fill-available;
	height: 400px;
	object-fit: contain;
}

#image-place {
	height: 400px;
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
	background: #6777ef !important;
	height: 40px;
	width: 40px;
	padding: 20px 20px;
	background-size: 20px;
	border-radius: 50%;
}

.table-striped>tbody>tr:nth-of-type(odd)>* {
	--bs-table-accent-bg: rgb(251 251 251) !important;
}

table.dataTable th,
table.dataTable td {
	padding: 10px 28px;
}


.iziToast.iziToast-color-green {
	border-left: 4px solid #40e378;
	backdrop-filter: blur(21px);
	box-shadow: 0px 10px 7px -10px #bfffd5;
	border-radius: 10px;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-ms-border-radius: 10px;
	-o-border-radius: 10px;
}

/* .btn.rejectReport svg,
.btn.edit svg,
.btn.delete svg,
.btn.repeat svg, */
.btn svg  {
	width: 100%;
	display: block;
	height: 18px;
	min-width: 25px;
	max-width: 25px;
	width: 25px;
}
.repeat svg {
	transition: 0.0s;
	-webkit-transition: 0.0s;
	-moz-transition: 0.0s;
	-ms-transition: 0.0s;
	-o-transition: 0.0s;
}
.repeat:hover svg {
	transition: 1s;
	-webkit-transition: 1s;
	-moz-transition: 1s;
	-ms-transition: 1s;
	-o-transition: 1s;
	transform: rotate(360deg);
	-webkit-transform: rotate(360deg);
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
}
td {
	font-weight: 500;
	font-size: 16px;
}

.dataTables_wrapper {
	position: relative;
}

.dataTables_processing {
	position: absolute;
}

.swal-button.swal-button--confirm {
	background: var(--bs-red);
	box-shadow: 0 2px 6px var(--bs-red);
}

.swal-button.swal-button--confirm:hover {
	background: #fb160a;
}

.btn-orange {
	background: var(--bs-orange);
}

.btn-orange:hover,
.btn-orange:focus-visible,
.btn-orange:active {
	background: #eb6b00 !important;
}

.form-control[type=file]:not(:disabled):not([readonly]) {
	height: 40px;
}
#imagePreviewModal .imagePreviewSub {
	position: relative;
	padding-top: 80%;
	display: block;
}

#imagePreviewModal .imagePreviewSub img {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	height: 100%;
	width: 100%;
	object-fit: contain;
}

.swal-icon--success~.swal-footer .swal-button.swal-button--confirm {
	background: var(--theme-color) !important;
	box-shadow: 0 2px 6px var(--theme-color);
}

.note-editor.note-airframe .note-editing-area .note-editable,
.note-editor.note-frame .note-editing-area .note-editable {
	padding: 0px 20px;
	overflow: auto;
	word-wrap: break-word;
	margin-top: 7px;
}

#profileVerificationTable img {
	cursor: pointer;
}

/* .same-height-card .card {
  height: 100%;
  } */
.dataTables_wrapper .row:nth-child(2) {
	margin-left: -37px;
	margin-right: -37px;
}

.paginate_button.disabled {
	cursor: not-allowed;
}

.card-tab .nav-tabs {
	border: none;
	background: #f5f5f5;
	display: inline-flex;
	padding: 5px 5px;
	border-radius: var(--border-radius);
	-webkit-border-radius: var(--border-radius);
	-moz-border-radius: var(--border-radius);
	-ms-border-radius: var(--border-radius);
	-o-border-radius: var(--border-radius);
}

.card-tab .nav-tabs .nav-item .nav-link {
	padding: 5px 30px;
	border-radius: var(--border-radius);
	font-weight: 500;
	-webkit-border-radius: var(--border-radius);
	-moz-border-radius: var(--border-radius);
	-ms-border-radius: var(--border-radius);
	-o-border-radius: var(--border-radius);
}

.card-tab .nav-tabs .nav-item .nav-link.active {
	background: var(--theme-color);
	color: var(--text-white);
}

.card-tab .nav-tabs .nav-link,
.card-tab .nav-tabs .nav-link {
    border: none;
	font-weight: 500;
    color: #000;
}
/* #post_contents {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  grid-auto-rows: minmax(30px, auto);
  width: 100%;
  margin-bottom: 20px;
  } */
.mySwiper {
	overflow: hidden;
	position: relative;
	box-shadow: 0 5px 20px 0 rgb(69 67 96 / 10%) !important;
}

.swiper-button {
	transform: translateY(50%);
	background: #efefef;
	width: 40px;
	height: 40px;
	border-radius: 50%;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	-ms-border-radius: 50%;
	-o-border-radius: 50%;
}

.swiper-pagination {
	background: #fff;
	position: absolute;
	bottom: -30px;
}

.swiper-button-next:after,
.swiper-button-prev:after {
	background-size: 50%;
	font-size: 19px;
	font-weight: 900;
	color: #000;
}

.swiper-slide {
	padding-top: 100%;
	position: relative;
	display: block;
}

.swiper-slide img,
.swiper-slide video {
	width: 100%;
	height: 100%;
	object-fit: contain;
	/*box-shadow: 0 5px 20px 0 rgb(69 67 96 / 10%) !important;
	*/border-radius: 20px;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-ms-border-radius: 20px;
	-o-border-radius: 20px;
}

.commonViewBtn {
	display: flex;
	align-items: center;
}

.commonViewBtn svg {
	margin-right: 10px;
}
.password-icon {
    position: absolute;
    justify-content: center;
    right: 0;
    top: 50%;
    transform: translateY(-33%);
}
.password-icon .feather-eye-off {
	display: none;
}
.password-icon svg {
	width: 18px;
    color: #000;
    display: flex;
    align-items: center;
    position: absolute;
    transition: color 0.2s;
    background: #ededed;
    width: 40px;
    height: 40px;
    justify-content: center;
    border-radius: 50%;
    right: 19px;
    top: 50%;
    transform: translateY(-33%);
    padding: 0 11px;
}
.password-icon:hover {
	cursor: pointer;
	color: var(--theme-color);
}

input:focus+p,
input:not(:placeholder-shown)+p {
	top: 0;
	font-size: 0.9rem;
	color: var(--theme-color);
}

input:not(:focus)+p {
	color: #7a7a7a56;
}

.password-icon .feather-eye-off {
	display: none;
}

.password-icon .feather-eye-off {
	display: none;
} 
.userDetailImage img {
	height: 100%;
    object-fit: cover;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0px 0px 20px -10px #b1b1b1;
    background: #d7d7d7;
}
.profileDetailImages {
    display: flex;
    /* align-items: center; */
}
.userDetailImageBg .img-fluid {
    width: 100%; 
	height: 250px;
} 
.userDetailImageBg {
	width: 100%;
	position: relative;
}

.avatar-upload {
	position: relative;
    max-width: 100%;
    margin: 0;
    height: 250px;
}
.avatar-upload .avatar-edit {
  position: absolute;
  right: 10px;
  z-index: 1;
  top: 10px;
}
.avatar-upload .avatar-edit label {
	padding: 4px 20px; 
    box-shadow: none;
}
.avatar-upload .avatar-edit input, 
.profilePicture .profile-edit input {
  display: none;
}
/* .avatar-upload .avatar-edit input + label {
  display: inline-block;
  width: 34px;
  height: 34px;
  margin-bottom: 0;
  border-radius: 100%;
  background: #FFFFFF;
  border: 1px solid transparent;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
  cursor: pointer;
  font-weight: normal;
  transition: all 0.2s ease-in-out;
} */
.avatar-upload .avatar-edit input + label:hover,
.profilePicture .profile-edit input + label:hover {
  background: #f1f1f1;
  border-color: #d6d6d6;
}
.avatar-upload .avatar-edit input + label:after,
.profilePicture .profile-edit input + label:after {
  color: #757575;
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  text-align: center;
  margin: auto;
}
.profilePicture .profile-edit {
	position: absolute;
    z-index: 1;
    top: auto;
    bottom: -22px;
    left: 50%;
    transform: translateX(-50%);
}
.avatar-upload .avatar-preview {
	border-radius: 15px;
	width: 100%;
    height: 100%;
    position: relative; 
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
}

.avatar-upload .avatar-preview > div {
	border-radius: 15px;
	width: 100%;
	height: 100%;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
}
 .profilePicture {
	position: relative;
    max-width: 100%;
    margin: 0; 
}
.profilePicture .profile-preview > div,
.profilePicture .profile-preview img {
	height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 20px;
    border: 5px solid #fff;
}
.profilePictureMain {
	position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%) translateY(0%);
}
.profile-preview {
    width: 120px;
    height: 120px;
}
.otherDetails {
    display: inline-block;
    width: auto;
    margin-bottom: 20px;
}
.otherDetails ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
}
.avatar-preview img {
    object-fit: cover;
    width: 100%;
    height: 100%;
    border-radius: 10px;
}
.file-input__input {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.file-input__label {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  font-size: 14px;
  padding: 10px 12px;
  background: #4245a8;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
}

.file-input__label svg {
  height: 16px;
  margin-right: 4px;
}

.otherDetails ul li {
    list-style: none;
    background: #e9ecef;
    padding: 10px 30px;
    margin-right: 20px;
    border-radius: 50px;
    /* box-shadow: 0px 0px 20px -10px #b1b1b1; */
	font-weight: 500;
}
.verified-badge {
	margin-left: 10px;
    width: 20px;
}
.otherDetails h5 {
    margin-bottom: 20px;
}
table.dataTable img.verified_icon_top {
	box-shadow: none;
    margin: 0;
    padding: 0;
    position: relative;
    top: -4px;
    left: 8px;
}
.btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.dashboard-cards {
	display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 35px;
    grid-auto-rows: minmax(50px, auto);
    width: 100%;
	margin-bottom: 30px;
}
.dashboard-blog {
    border: 2px solid #F7F7F7;
    /* box-shadow: 0px 10px 10px -10px #d7d7d7; */
    position: relative;
    border-radius: 20px;
}
.dashboard-blog-content-top {
	padding: 20px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.dashboard-blog-content {
    margin-top: 10px;
    padding: 0px 0px 15px 20px;
}
.dashboard-blog p {
	font-size: 30px;
    text-shadow: 2px 1px #e7e7e7;
    margin: 0;
    color: #000000;
    font-family: system-ui;
    font-weight: 500;
}
.dashboard-blog-content a {
    text-decoration: none;
    font-size: 18px;
    display: inline-block;
    width: 100%;
    position: relative;
    font-weight: 500;
}
.dashboard-blog-content a::before {
    content: '';
    position: absolute;
    top: 0;
    right: -1px;
    width: 1px;
    height: 100%;
    z-index: 1;
    transition: all 0.6s;
    -webkit-transition: all 0.6s;
    -moz-transition: all 0.6s;
    -o-transition: all 0.6s;
    -ms-transition: all 0.6s;
    background: var(--theme-color);
    box-shadow: 0px 0px 10px var(--theme-color);
}
.dashboard-blog-content a:hover::before {
    transform: translate(-150px, 0) rotate(270deg);
    -webkit-transform: translate(-150px, 0) rotate(270deg);
    -moz-transform: translate(-150px, 0) rotate(270deg);
    -ms-transform: translate(-150px, 0) rotate(270deg);
    -o-transform: translate(-150px, 0) rotate(270deg);
    background: var(--theme-color);
    box-shadow: none;
    width: 8px;
    height: 8px;
    border-radius: 100%;
    top: 10px;
}
.dashboard-blog-content a:hover {
    color: var(--theme-color);
    background: linear-gradient(to right, var(--theme-color-light) 0, var(--theme-color) 100%);
    -webkit-background-clip: text;
    -o-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: rgba(33, 0, 53, 0);
    text-decoration: none;
    transition: all 0.5s ease;
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
}
.card-icon {
    background: #e3eaef;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0px 10px 10px -10px #efefef;
}
.card-icon svg {
    width: 100%;
    max-width: 30px;
	stroke: #fff;
}
.dashboard-blog-content h4 {
    margin: 0;
	text-transform: capitalize;
	margin-bottom: 5px;
}
.card-icon i {
    color: #fff;
}
.dashboard-blog:nth-child(1) .card-icon {
	background: rgb(254,173,151);
	background: linear-gradient(90deg, rgba(254,173,151,1) 0%, rgba(247,128,157,1) 100%);
}
.dashboard-blog:nth-child(2) .card-icon {
	background: rgb(60,238,176);
	background: linear-gradient(90deg, rgba(60,238,176,1) 0%, rgba(66,213,224,1) 100%);
}
.dashboard-blog:nth-child(3) .card-icon {
	background: rgb(157,210,254);
background: linear-gradient(90deg, rgba(157,210,254,1) 0%, rgba(144,156,252,1) 100%);
}
.dashboard-blog:nth-child(4) .card-icon {
	background: rgb(255,210,123);
	background: linear-gradient(90deg, rgba(255,210,123,1) 0%, rgba(254,131,174,1) 100%);
}
.dashboard-blog:nth-child(5) .card-icon {
	background: rgb(60,238,176);
	background: linear-gradient(90deg, rgba(60,238,176,1) 0%, rgba(66,213,224,1) 100%);
}
.dashboard-blog:nth-child(6) .card-icon {
	background: rgb(245,169,200);
	background: linear-gradient(90deg, rgba(245,169,200,1) 0%, rgba(194,137,245,1) 100%);
}
.modal-header h5 {
	margin-bottom: 0;
}


@media only screen and (max-width: 1440px) {
	.dashboard-cards {
		grid-template-columns: repeat(4, 1fr);
		gap: 25px;
		grid-auto-rows: minmax(50px, auto);
	}
	.dashboard-blog-content a:hover::before {
		transform: translate(-120px, 0) rotate(270deg);
		-webkit-transform: translate(-120px, 0) rotate(270deg);
		-moz-transform: translate(-120px, 0) rotate(270deg);
		-ms-transform: translate(-120px, 0) rotate(270deg);
		-o-transform: translate(-120px, 0) rotate(270deg);
	}
}
@media only screen and (min-width: 992px) {
	.navbar-expand-lg .navbar-nav .dropdown-menu {
		position: absolute;
		right: 15px;
		margin-top: 5px;
		border-radius: 40px;
		width: 0px;
		padding: 0;
	}
}
@media only screen and (max-width: 1024px) {
	.dashboard-cards {
		grid-template-columns: repeat(2, 1fr);
	}
}
@media only screen and (max-width: 575px) {
	.dashboard-cards {
		grid-template-columns: repeat(1, 1fr);
	}
}
#userPostTable_filter {
	display: none;
}

/*  */
.modal.fade .modal-dialog {
    transform: scale(0.9);
    -webkit-transform: scale(0.9);
    -moz-transform: scale(0.9);
    -ms-transform: scale(0.9);
    -o-transform: scale(0.9);
    transition: transform .3s ease-in-out;
    -webkit-transition: transform .3s ease-in-out;
    -moz-transition: transform .3s ease-in-out;
    -ms-transition: transform .3s ease-in-out;
    -o-transition: transform .3s ease-in-out;
}
.modal.show .modal-dialog {
	transform: scale(1);
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transition: transform .3s ease-out;
    -webkit-transition: transform .3s ease-out;
    -moz-transition: transform .3s ease-out;
    -ms-transition: transform .3s ease-out;
    -o-transition: transform .3s ease-out;
}
.userLink {
    text-decoration: none;
}
.roomProfile {
	width: 140px;
	min-width: 140px;
	height: 140px;
	border-radius: 15px;
	object-fit: cover;
}
.room_interest .badge {
    padding: 10px 17px 8px;
}
.room-desc {
    font-size: 16px;
    line-height: initial;
}
.room-description {
    width: 80%;
}

.faqs_table_blog {
    display: grid;
    width: 80%;
}
.faqs_table_blog span.answer {
      white-space: break-spaces;
    position: relative;
    padding-left: 34px;
    color: #818181;
    font-weight: 400;
}
.faqs_table_blog span.question {
    font-weight: bold;
    font-size: 20px;
    position: relative;
    padding-left: 34px;
}
.faqs_table_blog span.question::before {
  content: "Q : ";
  position: absolute;
  left: 0;
}
.faqs_table_blog span.answer::before {
  content: "A : ";
  position: absolute;
  left: 0;
}
.nav-tabs .nav-link {
	border-radius: 20px;
}
.nav-tabs .nav-link.active {
	border-radius: 20px;
    font-weight: 500;
    background: var(--theme-color);
}
nav.card-tab {
    margin-bottom: 40px;
}
.item-description {
    width: 100%;
    display: inline-block;
    white-space: break-spaces;
}

/* .spinning {
  padding-right: 40px;
} */
.spinning:before {
	content: "";
	width: 15px;
	height: 15px;
	border-radius: 50%;
	border: 2px solid #181620;
	border-right: 3px solid #ffffff;
	-webkit-animation: rotate360 0.5s infinite linear, exist 0.1s forwards ease;
	animation: rotate360 0.5s infinite linear, exist 0.1s forwards ease;
	position: relative;
	left: -10px;
	top: 0px;
}
@-webkit-keyframes rotate360 {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotate360 {
  100% {
    transform: rotate(360deg);
  }
}

.story_content_view {
    width: 100%;
    height: 450px;
    object-fit: contain;
    box-shadow: 0 5px 20px 0 rgb(69 67 96 / 10%) !important;
}

/* Responsive */
@media (max-width: 1440px) {

  th.music_td {
    width: 340px !important;
    min-width: 340px !important;
  }
}

@media (max-width: 1024px) {
  .main-sidebar .sidebar-brand {
    display: none;
  }
  .navbar {
    left: 0;
  }
  .main-content {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (max-width: 480px) {

  .dataTables_filter {
    margin-bottom: 20px;
  }
  ul.pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  .dataTables_info {
    text-align: center;
  }
  .pagination .paginate_button .page-link {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .card-header .btn.theme-btn {
    font-size: 12px;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link-user:focus-visible {
    outline: none !important;
  }
}

