# 🚀 Orange Backend - Coolify Deployment Guide

## ✅ Database Connection Status: CONFIRMED

The database connection has been successfully tested and verified:
- **Host**: server.wattlesol.com:3306
- **Database**: default
- **Status**: ✅ **CONNECTED & WORKING**
- **Tables**: All required custom tables created automatically

## 📋 Pre-Deployment Checklist

- [x] Database connection tested and confirmed
- [x] Docker configuration optimized for production
- [x] Database schema setup automated
- [x] Environment variables configured
- [x] Health checks implemented
- [x] Apache configuration optimized for Laravel

## 🐳 Docker Deployment on Coolify

### Step 1: Environment Variables

Configure these environment variables in your Coolify application:

```env
# Application
APP_NAME=Orange
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database (CONFIRMED WORKING)
DB_CONNECTION=mysql
DB_HOST=server.wattlesol.com
DB_PORT=3306
DB_DATABASE=default
DB_USERNAME=root
DB_PASSWORD=AWYdRs8wU9rUGgK32A8dffbbJbW8uiD2Bd2opseceduMY1emX04R98inLNiYB96d

# Cache & Sessions
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=error

# Mail (Optional)
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
```

### Step 2: Coolify Configuration

1. **Create New Application**
   - Type: Docker
   - Repository: Your Git repository
   - Branch: main/master

2. **Build Settings**
   - Dockerfile: `./Dockerfile` (auto-detected)
   - Build Context: `.`
   - Port: `80`

3. **Environment Variables**
   - Copy the variables above into Coolify's environment section
   - The APP_KEY will be generated automatically during deployment

### Step 3: Deploy

Click "Deploy" in Coolify. The deployment process will:

1. ✅ Build the Docker image
2. ✅ Test database connection
3. ✅ Create all required database tables
4. ✅ Generate application key
5. ✅ Run Laravel migrations
6. ✅ Optimize application for production
7. ✅ Start the web server

## 🗄️ Database Schema

The following tables are automatically created:

| Table | Purpose |
|-------|---------|
| `admin_user` | Admin panel users |
| `appdata` | Application settings |
| `users` | App users (extended) |
| `interests` | User interests |
| `diamond_packs` | Virtual currency |
| `gifts` | Virtual gifts |
| `live_applications` | Live streaming requests |
| `posts` | User content |
| `reports` | Content reports |
| `redeem_requests` | Withdrawal requests |

## 🔐 Default Admin Access

After deployment, access the admin panel:
- **URL**: `https://your-domain.com/`
- **Username**: `admin`
- **Password**: `admin123`

**⚠️ IMPORTANT**: Change these credentials immediately after first login!

## 🏥 Health Monitoring

The container includes built-in health checks:
- Web server status
- Database connectivity
- Application responsiveness

Monitor these in your Coolify dashboard.

## 🔧 Application Features

- **Admin Dashboard**: Complete management interface
- **User Management**: Registration, verification, blocking
- **Live Streaming**: Application approval system
- **Content Management**: Posts, stories, comments
- **Virtual Economy**: Diamonds, coins, gifts
- **Reporting System**: User and content moderation
- **Notifications**: Push notification management

## 🐛 Troubleshooting

### Common Issues:

1. **500 Error on First Load**
   - Wait 30-60 seconds for full initialization
   - Check logs for APP_KEY generation

2. **Database Connection Failed**
   - Verify environment variables are correct
   - Check if database server is accessible

3. **Permission Errors**
   - Container automatically sets correct permissions
   - Restart deployment if needed

### Checking Logs:

```bash
# In Coolify, view application logs
# Look for these success messages:
✅ Database connection successful!
✅ Database tables created
✅ Application key generated
🚀 Application ready! Starting Apache...
```

## 📊 Performance Optimization

The Docker image includes:
- Optimized Composer autoloader
- PHP OPcache enabled
- Apache configuration tuned for Laravel
- Production-ready caching

## 🔄 Updates & Maintenance

To update the application:
1. Push changes to your Git repository
2. Trigger redeploy in Coolify
3. Database schema updates are handled automatically

## 📞 Support

If you encounter issues:
1. Check Coolify application logs
2. Verify environment variables
3. Ensure database connectivity
4. Review this deployment guide

---

**Status**: ✅ Ready for Production
**Last Tested**: Database connection successful
**Deployment Method**: Docker on Coolify
**Estimated Deploy Time**: 3-5 minutes
