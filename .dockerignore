# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md

# Node modules and build artifacts
node_modules
npm-debug.log
yarn-error.log

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Laravel specific
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Environment files (will be provided by Coolify)
.env.local
.env.*.local

# Testing
/tests
phpunit.xml
.phpunit.result.cache

# Development tools
webpack.mix.js
package.json
package-lock.json

# Vendor directory (will be installed during build)
/vendor

# Temporary files
*.tmp
*.temp
